# Feature Request: Goals Management

## Overview

This document outlines the implementation of a comprehensive goals management feature for the Finanze.Pro application. The feature will allow users to create, manage, and track financial goals with support for target amounts, target dates, and progress tracking.

## Feature Description

The goals feature enables users to:

- Create and manage multiple financial goals
- Set target amounts and target dates for goals (optional)
- Track progress towards goals with current amount
- Assign specific accounts to track goal progress
- Enable/disable goals as needed
- View goal details and progress history

## Technical Requirements

### Base Types (Already Defined)

The base types are already defined in `src/features/goals/types.ts`:

```typescript
export interface Goal {
  id: string;
  name: string;
  color: string;
  description: string | null;
  target_amount: string | null;
  target_date: string | null;
  current_amount: string;
  is_active: boolean;
  account_ids: string[];
  created_at: string;
  updated_at: string;
}
```

### API Endpoints

Following the existing API patterns (`/v1/` prefix):

#### Goal CRUD Operations

- `GET /v1/goals` - List all goals
- `POST /v1/goals` - Create new goal
- `GET /v1/goals/{goalId}` - Get specific goal
- `PUT /v1/goals/{goalId}` - Update goal
- `DELETE /v1/goals/{goalId}` - Delete goal

### Validation Schema

Following the existing schema patterns, create `src/features/goals/schemas.ts`:

```typescript
export const GoalCreateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  color: hexColor(),
  target_amount: decimal()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || Number(value) >= 0.01, {
      message: "Target amount must be at least 0.01",
    }),
  target_date: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || z.string().datetime().safeParse(value).success, {
      message: "Target date must be a valid date",
    }),
});

export const GoalUpdateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  color: hexColor(),
  target_amount: decimal()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || Number(value) >= 0.01, {
      message: "Target amount must be at least 0.01",
    }),
  target_date: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || z.string().datetime().safeParse(value).success, {
      message: "Target date must be a valid date",
    }),
  is_active: z.boolean(),
});
```

### Hooks Implementation

Following the existing hook patterns:

#### Core Hooks

- `useGoals()` - Fetch all goals
- `useGoal(goalId)` - Fetch specific goal
- `useGoalCreate()` - Create goal mutation
- `useGoalUpdate(goalId)` - Update goal mutation
- `useGoalDelete(goalId)` - Delete goal mutation
- `useGoalActions(goal?: Goal)` - Actions for create/edit/delete. Lightweight wrapper around store actions.

### Component Structure

Following the existing component organization patterns:

#### Core Components

- `GoalDialog` - Main dialog container with mode switching
- `GoalFormCreate` - Create goal form
- `GoalFormEdit` - Edit goal form
- `GoalFormDelete` - Delete confirmation form
- `GoalsList` - List of goals with actions
- `GoalsListItem` - Individual goal item
- `GoalDetailsOverview` - Goal details and progress display

### Routing

Following the existing routing structure:

- `/goals` - Main goals list page (`src/routes/_auth/goals.index.tsx`)
- `/goals/{goalId}` - Goal details page (`src/routes/_auth/goals.$goalId.tsx`)

### UI/UX Requirements

#### Goal List Page

- Display all goals in a list format
- Show goal name, description, progress, and target information
- Include action buttons for edit/delete
- Add "Create Goal" button
- Toggle between active/inactive goals
- Progress indicators for each goal

#### Goal Details Page

- Display comprehensive goal information
- Show progress visualization
- Display associated accounts
- Include edit/delete actions
- Show creation and update timestamps

#### Goal Forms

- **Create Form**: name, description, color, target_amount, target_date
- **Edit Form**: same fields as create + is_active toggle
- **Delete Form**: Confirmation dialog with goal name

### Navigation Integration

Add goals to the navigation structure:

- Add to main navigation (`src/routes/_auth/index.tsx`)
- Add to sidebar navigation (`src/components/layouts/sidebar.tsx`)
- Add appropriate icon (suggest `TargetIcon` from lucide-react)
- Position between budgets and categories in navigation order

## Implementation Plan

### Phase 1: Foundation

1. Create validation schemas (`src/features/goals/schemas.ts`)
2. Implement store with Zustand (`src/features/goals/store.ts`)
3. Create basic API hooks (`src/features/goals/hooks/`)
4. Set up routing structure

### Phase 2: Components

1. Implement CRUD forms
2. Build goal list components
3. Create dialog containers
4. Add progress visualization components

### Phase 3: Pages & Integration

1. Implement goal list page
2. Implement goal details page
3. Add navigation integration
4. Integrate with existing UI patterns

### Phase 4: Polish & Testing

1. Add comprehensive error handling
2. Implement loading states
3. Add toast notifications
4. Write tests
5. UI/UX refinements

## Dependencies

- Existing UI components (Button, Dialog, Form, etc.)
- Existing input components patterns
- API client and query patterns
- Validation utilities (zod, schemas)
- State management (Zustand)
- Routing (TanStack Router)
- Date handling utilities
- Progress visualization components

## Notes

- The feature follows all existing patterns and conventions
- Uses the established component structure and naming
- Maintains consistency with other features (accounts, transactions, categories, budgets)
- Leverages existing UI components and patterns
- Follows the established API and validation patterns
- Target date is optional and stored as ISO string
- Target amount is optional and stored as decimal string
- Progress calculation based on current_amount vs target_amount
- Color picker integration for goal visualization
- Account association for tracking goal progress
