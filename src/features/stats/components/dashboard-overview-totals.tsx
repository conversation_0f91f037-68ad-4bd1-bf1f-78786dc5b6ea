import type { StatsOverview } from "../types";

import LoadingIndicator from "~/components/elements/loading-indicator";
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

interface Props {
  stats: StatsOverview | undefined;
  isLoading: boolean;
}

export default function DashboardOverviewTotals({ stats, isLoading }: Props) {
  const baseCurrency = useBaseCurrency();

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (!stats) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardDescription>Total Balance</CardDescription>
        <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
          {formatCurrency(baseCurrency, stats.total_balance)}
        </CardTitle>
      </CardHeader>
      <CardFooter className="flex-col items-start gap-1.5 text-sm">
        <div className="line-clamp-1">
          Total <span className="text-primary font-medium">{formatCurrency(baseCurrency, stats.total_savings)}</span> in
          your savings
        </div>
        <div className="text-muted-foreground">You are doing well!</div>
      </CardFooter>
    </Card>
  );
}
