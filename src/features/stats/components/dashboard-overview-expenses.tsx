import type { StatsOverview } from "../types";

import { TrendingDownIcon, TrendingUpIcon } from "lucide-react";

import LoadingIndicator from "~/components/elements/loading-indicator";
import { Badge } from "~/components/ui/badge";
import { Card, CardAction, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatPercentage } from "~/lib/formatters";

interface Props {
  stats: StatsOverview | undefined;
  isLoading: boolean;
}

export default function DashboardOverviewExpenses({ stats, isLoading }: Props) {
  const baseCurrency = useBaseCurrency();

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (!stats) {
    return null;
  }

  // Calculate the percentage difference between current and previous month expenses
  const currentExpenses = parseFloat(stats.current_month_expenses);
  const previousExpenses = parseFloat(stats.previous_month_expenses);

  // Calculate percentage difference
  let percentageDifference = 0;
  if (previousExpenses !== 0) {
    percentageDifference = ((currentExpenses - previousExpenses) / Math.abs(previousExpenses)) * 100;
  }

  const isIncrease = percentageDifference > 0;

  return (
    <Card>
      <CardHeader>
        <CardDescription>Current Month Expenses</CardDescription>
        <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
          {formatCurrency(baseCurrency, stats.current_month_expenses)}
        </CardTitle>
        <CardAction>
          <Badge variant="outline" className={isIncrease ? "text-red" : "text-green"}>
            {isIncrease ? <TrendingUpIcon /> : <TrendingDownIcon />}
            {formatPercentage(percentageDifference.toFixed(2))}
          </Badge>
        </CardAction>
      </CardHeader>
      <CardFooter className="flex-col items-start gap-1.5 text-sm">
        <div className="line-clamp-1 flex gap-2 font-medium">
          {isIncrease ? (
            <>
              Trending up this month <TrendingUpIcon className="size-4" />
            </>
          ) : (
            <>
              Trending down this month <TrendingDownIcon className="size-4" />
            </>
          )}
        </div>
        <div className="text-muted-foreground">
          <span className={isIncrease ? "text-red" : "text-green"}>
            {formatPercentage(percentageDifference.toFixed(2))}
          </span>{" "}
          compared to last month
        </div>
      </CardFooter>
    </Card>
  );
}
