import type { Budget } from "../types";

import { Link } from "@tanstack/react-router";
import { EditIcon, MoreHorizontalIcon, TrashIcon, ViewIcon } from "lucide-react";

import DefinitionBlock from "~/components/blocks/definition-block";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useBudgetActions } from "../hooks";
import { periodTypesMap } from "../schemas";

interface Props {
  budget: Budget;
}

export default function BudgetsListItem({ budget }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editBudget, deleteBudget } = useBudgetActions(budget);

  return (
    <div>
      <div className="bg-card flex items-center justify-between rounded-lg px-4 py-3">
        <div>
          <Link
            to="/budgets/$budgetId"
            params={{ budgetId: budget.id }}
            className="text-link hover:text-link/90 text-md/6 block flex-grow font-medium hover:underline"
          >
            {budget.name}
          </Link>
          {budget.description && <p className="text-muted-foreground text-sm">{budget.description}</p>}
        </div>

        <div className="flex items-center gap-4">
          <DefinitionBlock size="md" title="Planned" className="w-40">
            {formatCurrency(baseCurrency, budget.current_period.planned_amount)}
          </DefinitionBlock>

          <DefinitionBlock size="md" title="Used" className="w-40">
            {formatCurrency(baseCurrency, budget.current_period.used_amount)}
          </DefinitionBlock>

          <DefinitionBlock size="md" title="Period" className="w-32">
            {periodTypesMap[budget.period_type]}
          </DefinitionBlock>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to="/budgets/$budgetId" params={{ budgetId: budget.id }}>
                  <ViewIcon />
                  <span className="inline-block pt-0.5">Budget details</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={editBudget}>
                <EditIcon />
                <span className="inline-block pt-0.5">Edit budget</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={deleteBudget} variant="destructive">
                <TrashIcon />
                <span className="inline-block pt-0.5">Delete</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
