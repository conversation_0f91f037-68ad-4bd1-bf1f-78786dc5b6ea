import type { Budget } from "../types";

import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useBudgetDelete } from "../hooks";
import useBudgetsStore from "../store";

interface Props {
  budget: Budget;
}

export default function BudgetFormDelete({ budget }: Props) {
  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  const { mutate: deleteBudget, isPending, error } = useBudgetDelete(budget.id);

  return (
    <div className="space-y-4">
      {error && <ErrorMessage title="Can't delete budget" error={error} />}

      <div className="space-y-2">
        <p>Are you sure you want to delete this budget?</p>
        <div className="bg-muted rounded-md p-3">
          <p className="font-medium">{budget.name}</p>
          {budget.description && <p className="text-muted-foreground text-sm">{budget.description}</p>}
        </div>
        <p className="text-muted-foreground text-sm">
          This action cannot be undone. All historical data for this budget will be permanently deleted.
        </p>
      </div>

      <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
        <Button type="button" variant="destructive" onClick={() => deleteBudget()} disabled={isPending}>
          {isPending ? <LoaderIcon className="animate-spin" /> : "Delete Budget"}
        </Button>
        <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} disabled={isPending}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
