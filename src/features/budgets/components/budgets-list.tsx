import type { Budget } from "../types";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";

import { useBudgetActions } from "../hooks";
import BudgetsListItem from "./budgets-list-item";

interface Props {
  budgets: Budget[];
}

export default function BudgetsList({ budgets }: Props) {
  const { createBudget } = useBudgetActions();

  if (budgets.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="space-y-4">
          <p className="text-muted-foreground">No budgets found</p>
          <Button onClick={() => createBudget()}>
            <PlusIcon />
            <span className="inline-block pt-0.5">Create Your First Budget</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {budgets.map((budget) => (
        <BudgetsListItem key={budget.id} budget={budget} />
      ))}
    </div>
  );
}
