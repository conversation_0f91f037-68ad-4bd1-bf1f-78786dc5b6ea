import type { Budget, BudgetUpdateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2Icon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputSwitch from "~/components/inputs/input-switch";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useBudgetUpdate } from "../hooks";
import { BudgetUpdateSchema } from "../schemas";
import useBudgetsStore from "../store";

interface Props {
  budget: Budget;
}

export default function BudgetFormEdit({ budget }: Props) {
  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  const { mutate: updateBudget, isPending, error } = useBudgetUpdate(budget.id);

  const defaultValues = useMemo<BudgetUpdateData>(
    () => ({
      name: budget.name,
      description: budget.description || "",
      amount: budget.amount,
      is_active: budget.is_active,
    }),
    [budget]
  );

  const form = useForm<BudgetUpdateData>({
    defaultValues,
    resolver: zodResolver(BudgetUpdateSchema),
  });

  const isActive = form.watch("is_active", defaultValues.is_active);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => updateBudget(data))} className="space-y-4">
        {error && <ErrorMessage title="Can't update budget" error={error} />}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Budget Name"
            placeholder="e.g., Groceries, Entertainment"
            className="md:col-span-2"
            disabled={isPending}
            required
          />

          <InputTextarea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Budget description (optional)"
            className="md:col-span-2"
            disabled={isPending}
          />

          <InputText
            control={form.control}
            name="amount"
            label={budget.is_percentage ? "Percentage (%)" : "Amount"}
            placeholder="0.00"
            type="number"
            min={0.01}
            max={budget.is_percentage ? 100 : undefined}
            step="0.01"
            disabled={isPending}
            required
          />

          <InputSwitch
            control={form.control}
            name="is_active"
            label={isActive ? "Budget is active" : "Budget is disabled"}
            description={
              isActive
                ? "Disable to stop tracking spending against this budget"
                : "Enable to make this budget active and track spending against it"
            }
            disabled={isPending}
            className="md:col-span-2"
          />
        </div>

        <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
          <Button type="submit" disabled={isPending}>
            {isPending ? <Loader2Icon className="animate-spin" /> : "Update Budget"}
          </Button>
          <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} disabled={isPending}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
