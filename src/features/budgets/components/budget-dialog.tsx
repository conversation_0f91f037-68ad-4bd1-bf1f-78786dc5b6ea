import { useShallow } from "zustand/react/shallow";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import useBudgetsStore from "../store";
import BudgetFormCreate from "./budget-form-create";
import BudgetFormDelete from "./budget-form-delete";
import BudgetFormEdit from "./budget-form-edit";

const getDialogTitle = (dialogMode: "create" | "edit" | "delete") => {
  switch (dialogMode) {
    case "create":
      return "Create Budget";
    case "edit":
      return "Edit Budget";
    case "delete":
      return "Delete Budget";
    default:
      return "Budget";
  }
};

const getDialogDescription = (dialogMode: "create" | "edit" | "delete") => {
  switch (dialogMode) {
    case "create":
      return "Create a new budget to track your spending.";
    case "edit":
      return "Update your budget details.";
    case "delete":
      return "This action cannot be undone.";
    default:
      return "";
  }
};

export default function BudgetDialog() {
  const { dialogOpen, dialogMode, currentBudget, closeDialog } = useBudgetsStore(useShallow((state) => ({ ...state })));

  return (
    <Dialog open={dialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getDialogTitle(dialogMode)}</DialogTitle>
          <DialogDescription>{getDialogDescription(dialogMode)}</DialogDescription>
        </DialogHeader>

        {dialogMode === "create" && <BudgetFormCreate />}
        {dialogMode === "edit" && currentBudget && <BudgetFormEdit budget={currentBudget} />}
        {dialogMode === "delete" && currentBudget && <BudgetFormDelete budget={currentBudget} />}
      </DialogContent>
    </Dialog>
  );
}
