import type { Budget } from "../types";

import { useMemo } from "react";

import { format } from "date-fns";
import { ArchiveIcon, CheckCircleIcon as CheckIcon, EditIcon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import Box from "~/components/blocks/box";
import DefinitionBlock from "~/components/blocks/definition-block";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { useBudgetActions } from "../hooks";
import { periodTypesMap } from "../schemas";

interface Props {
  budget: Budget;
}

export default function BudgetDetailsOverview({ budget }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editBudget, deleteBudget } = useBudgetActions(budget);

  const usagePercentage = useMemo(() => {
    const planned = Number(budget.current_period.planned_amount);
    const used = Number(budget.current_period.used_amount);
    
    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [budget.current_period]);

  const periodStart = format(new Date(budget.current_period.period_start), "MMM d, yyyy");
  const periodEnd = format(new Date(budget.current_period.period_end), "MMM d, yyyy");

  return (
    <Box className="flex flex-col gap-6 border-t-[6px] border-t-primary ps-8 pe-4 pt-4 pb-8">
      <div className="flex items-center gap-4">
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(budget.created_at)}
        </p>
        <Badge variant="secondary" size="lg">
          {periodTypesMap[budget.period_type]}
        </Badge>
        {budget.is_active && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <CheckIcon className="size-4" />
            Active
          </p>
        )}
        {!budget.is_active && (
          <p className="text-gray flex items-center gap-1 text-sm/5">
            <ArchiveIcon className="size-4" />
            Inactive
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editBudget}>
              <EditIcon /> <span className="inline-block pt-0.5">Edit budget</span>
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" onClick={deleteBudget}>
              <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{budget.name}</h2>
        {budget.description && <p className="mt-1 line-clamp-2 text-base text-gray-600">{budget.description}</p>}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title="Budget Amount">
          <p className="flex items-end gap-2">
            {budget.is_percentage ? (
              <span>{budget.amount}% of income</span>
            ) : (
              formatCurrency(baseCurrency, budget.amount)
            )}
          </p>
        </DefinitionBlock>
        <DefinitionBlock title="Current Period">
          <p>{periodStart} - {periodEnd}</p>
        </DefinitionBlock>
      </div>

      <div className="space-y-4">
        <h3 className="font-medium">Current Period Usage</h3>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Used / Planned</span>
            <span>
              {formatCurrency(baseCurrency, budget.current_period.used_amount)} / {formatCurrency(baseCurrency, budget.current_period.planned_amount)}
            </span>
          </div>
          <Progress value={usagePercentage} className="h-3" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{usagePercentage.toFixed(1)}% used</span>
            <span>
              {usagePercentage > 100 ? "Over budget" : usagePercentage === 100 ? "Budget met" : "Under budget"}
            </span>
          </div>
        </div>
      </div>
    </Box>
  );
}
