import { useState } from "react";

import PaginationBlock from "~/components/blocks/pagination-block";
import LoadingIndicator from "~/components/elements/loading-indicator";

import { useBudgetHistory } from "../hooks";
import BudgetHistoryItem from "./budget-history-item";

interface Props {
  budgetId: string;
}

export default function BudgetHistoryList({ budgetId }: Props) {
  const [currentPage, setCurrentPage] = useState(1);
  const perPage = 10;

  const { history, pagination, isLoading, error } = useBudgetHistory({
    budgetId,
    page: currentPage,
    per_page: perPage,
  });

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (error) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">Failed to load budget history</p>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">No budget history found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {history.map((period) => (
          <BudgetHistoryItem key={period.id} period={period} />
        ))}
      </div>

      {pagination && (
        <PaginationBlock pagination={pagination} currentPage={currentPage} onPageChange={setCurrentPage} />
      )}
    </div>
  );
}
