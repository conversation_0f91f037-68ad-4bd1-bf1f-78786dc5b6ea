import type { BudgetPeriod } from "../types";

import { useMemo } from "react";

import { format } from "date-fns";

import { Progress } from "~/components/ui/progress";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

interface Props {
  period: BudgetPeriod;
}

export default function BudgetHistoryItem({ period }: Props) {
  const baseCurrency = useBaseCurrency();

  const usagePercentage = useMemo(() => {
    const planned = Number(period.planned_amount);
    const used = Number(period.used_amount);

    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [period]);

  const periodStart = format(new Date(period.period_start), "MMM d, yyyy");
  const periodEnd = format(new Date(period.period_end), "MMM d, yyyy");

  return (
    <div className="bg-card rounded-lg border p-4">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">
            {periodStart} - {periodEnd}
          </h4>
          <span className="text-muted-foreground text-sm">
            {formatCurrency(baseCurrency, period.used_amount)} / {formatCurrency(baseCurrency, period.planned_amount)}
          </span>
        </div>

        <div className="space-y-2">
          <Progress value={usagePercentage} className="h-2" />
          <div className="text-muted-foreground flex justify-between text-xs">
            <span>{usagePercentage.toFixed(1)}% used</span>
            <span>
              {usagePercentage > 100 ? "Over budget" : usagePercentage === 100 ? "Budget met" : "Under budget"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
