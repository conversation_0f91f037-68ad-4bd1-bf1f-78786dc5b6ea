import type { Budget, BudgetCreateData } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete";

interface State {
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentBudget?: Budget;
  initialValues?: Partial<BudgetCreateData>;
}

interface Actions {
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentBudget: (budget?: Budget) => void;
  setInitialValues: (initialValues?: Partial<BudgetCreateData>) => void;
  closeDialog: () => void;
}

const useBudgetsStore = create<State & Actions>()(
  immer((set) => ({
    dialogOpen: false,
    dialogMode: "create",

    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentBudget: (budget) => {
      set({ currentBudget: budget });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentBudget: undefined, initialValues: undefined });
    },
  }))
);

export default useBudgetsStore;
