import type { PaginatedResponse } from "~/types";

import { z } from "zod";

import { BudgetCreateSchema, BudgetUpdateSchema, periodTypes } from "./schemas";

export type PeriodType = (typeof periodTypes)[number];

export interface BudgetPeriod {
  id: string;
  period_start: string;
  period_end: string;
  planned_amount: string;
  used_amount: string;
  created_at: string;
  updated_at: string;
}

export interface Budget {
  id: string;
  name: string;
  description: string | null;
  period_type: PeriodType;
  is_percentage: boolean;
  amount: string;
  current_period: BudgetPeriod;
  is_active: boolean;
  included_accounts: string[];
  created_at: string;
  updated_at: string;
}

export type BudgetCreateData = z.infer<typeof BudgetCreateSchema>;
export type BudgetUpdateData = z.infer<typeof BudgetUpdateSchema>;

export type BudgetsResponse = Budget[];
export type BudgetHistoryResponse = PaginatedResponse<BudgetPeriod>;
