import type { BudgetHistoryResponse } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

interface UseBudgetHistoryParams {
  budgetId: string;
  page?: number;
  per_page?: number;
}

export function useBudgetHistory({ budgetId, page = 1, per_page = 10 }: UseBudgetHistoryParams) {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<BudgetHistoryResponse>(`/v1/budgets/${budgetId}/history?page=${page}&per_page=${per_page}`),
    queryKey: ["budgets", budgetId, "history", page, per_page],
    staleTime: 60 * 1000, // 1 minute
    enabled: !!budgetId,
  });

  const history = useMemo(() => data?.items ?? [], [data]);
  const pagination = useMemo(() => data?.pagination, [data]);

  return { history, pagination, isFetching, isLoading, error };
}
