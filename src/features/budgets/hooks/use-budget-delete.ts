import type { ApiError } from "~/api/client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useBudgetsStore from "../store";

export function useBudgetDelete(budgetId: string) {
  const queryClient = useQueryClient();

  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<void, ApiError>({
    mutationFn: () => apiClient.delete(`/v1/budgets/${budgetId}`),
    onSuccess: () => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["budgets"] }),
        queryClient.removeQueries({ queryKey: ["budgets", budgetId] }),
      ]);

      toast.success("Budget deleted", { description: "Budget deleted successfully." });

      setDialogOpen(false);
    },
  });
}
