import type { ApiError } from "~/api/client";
import type { Budget, BudgetUpdateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useBudgetsStore from "../store";

export function useBudgetUpdate(budgetId: string) {
  const queryClient = useQueryClient();

  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Budget, ApiError, BudgetUpdateData>({
    mutationFn: (data) => apiClient.put(`/v1/budgets/${budgetId}`, data),
    onSuccess: (budget) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["budgets"] }),
        queryClient.invalidateQueries({ queryKey: ["budgets", budgetId] }),
      ]);

      toast.success("Budget updated", { description: `Budget ${budget.name} updated successfully.` });

      setDialogOpen(false);
    },
  });
}
