import type { Budget } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useBudget(budgetId: string) {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Budget>(`/v1/budgets/${budgetId}`),
    queryKey: ["budgets", budgetId],
    staleTime: 60 * 1000, // 1 minute
    enabled: !!budgetId,
  });

  return { budget: data, isFetching, isLoading, error };
}
