import type { Budget, BudgetCreateData } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import useBudgetsStore from "../store";

export function useBudgetActions(budget?: Budget) {
  const { setDialogOpen, setDialogMode, setCurrentBudget, setInitialValues } = useBudgetsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentBudget: state.setCurrentBudget,
      setInitialValues: state.setInitialValues,
    }))
  );

  const createBudget = useCallback(
    (initialValues?: Partial<BudgetCreateData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editBudget = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentBudget(budget);
  }, [setDialogOpen, setDialogMode, setCurrentBudget, budget]);

  const deleteBudget = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentBudget(budget);
  }, [setDialogOpen, setDialogMode, setCurrentBudget, budget]);

  return { createBudget, editBudget, deleteBudget };
}
