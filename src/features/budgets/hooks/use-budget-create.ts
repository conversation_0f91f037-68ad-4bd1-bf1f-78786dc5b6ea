import type { ApiError } from "~/api/client";
import type { Budget, BudgetCreateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useBudgetsStore from "../store";

export function useBudgetCreate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Budget, ApiError, BudgetCreateData>({
    mutationFn: (data) => apiClient.post("/v1/budgets", data),
    onSuccess: (budget) => {
      void queryClient.invalidateQueries({ queryKey: ["budgets"] });

      toast.success("Budget created", { description: `Budget ${budget.name} created successfully.` });

      setDialogOpen(false);
    },
  });
}
