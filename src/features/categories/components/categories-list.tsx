import type { Category } from "../types";

import { useState } from "react";

import { ChevronDownIcon, ChevronRightIcon, MoreHorizontalIcon, PlusIcon } from "lucide-react";

import DefinitionBlock from "~/components/blocks/definition-block";
import { Button } from "~/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { useCategoryActions } from "../hooks";
import CategoriesListItem from "./categories-list-item";

interface Props {
  categories: Category[];
  title: string;
  isExpense?: boolean;
}

export default function CategoriesList({ categories, title, isExpense = true }: Props) {
  const { createCategory } = useCategoryActions();

  const [isOpen, setOpen] = useState(true);

  return (
    <Collapsible open={isOpen} onOpenChange={setOpen}>
      <div className="border-l-primary border-l-4">
        <div className="bg-card flex items-center justify-between rounded-r-lg px-2 py-3">
          <div className="flex items-center gap-1">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="icon">
                {isOpen ? <ChevronDownIcon className="size-4" /> : <ChevronRightIcon className="size-4" />}
              </Button>
            </CollapsibleTrigger>

            <div className="flex items-center gap-1">
              <div className="flex flex-col items-start">
                <h2 className="text-foreground text-xl/6 font-semibold">{title}</h2>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <DefinitionBlock title="Total" size="md">
              {categories.length}
            </DefinitionBlock>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontalIcon className="size-4" />
                  <span className="sr-only">Categories type actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => createCategory({ is_expense: isExpense })}>
                  <PlusIcon className="size-4" />
                  Add category
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <CollapsibleContent>
        <div className="mt-2 space-y-1">
          {categories.length > 0 &&
            categories.map((category) => <CategoriesListItem category={category} key={category.id} />)}
          {categories.length == 0 && (
            <div className="text-muted-foreground py-8 text-center">
              <p>No categories of this type</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => createCategory({ is_expense: isExpense })}
              >
                <PlusIcon className="mr-2 size-4" />
                Add category
              </Button>
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
