import LoadingIndicator from "~/components/elements/loading-indicator";

import { useCategories } from "../hooks";
import CategoriesList from "./categories-list";

export default function CategoriesView() {
  const { expenseCategories, incomeCategories, isLoading } = useCategories();

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      <CategoriesList categories={expenseCategories} title="Expenses" />
      <CategoriesList categories={incomeCategories} title="Income" isExpense={false} />
    </div>
  );
}
