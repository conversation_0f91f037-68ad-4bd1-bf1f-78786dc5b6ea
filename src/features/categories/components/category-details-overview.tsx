import type { Category } from "../types";

import { BanknoteArrowDownIcon, BanknoteArrowUpIcon, EditIcon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import Box from "~/components/blocks/box";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import CategoryIcon from "~/features/categories/components/category-icon";
import { formatDate } from "~/lib/formatters";

import { useCategoryActions } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoryDetailsOverview({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions(category);

  return (
    <Box className="flex flex-col gap-6 border-t-[6px] ps-8 pe-4 pt-4 pb-8" style={{ borderColor: category.color }}>
      <div className="flex items-center gap-4">
        <CategoryIcon category={category} className="size-4" />
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(category.created_at)}
        </p>
        {category.is_expense && (
          <p className="text-red flex items-center gap-1 text-sm/5">
            <BanknoteArrowDownIcon className="size-4" />
            Expense
          </p>
        )}
        {!category.is_expense && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <BanknoteArrowUpIcon className="size-4" />
            Income
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editCategory}>
              <EditIcon /> <span className="inline-block pt-0.5">Edit category</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={deleteCategory} variant="destructive">
              <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{category.name}</h2>
      </div>
    </Box>
  );
}
