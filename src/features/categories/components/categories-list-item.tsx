import type { Category } from "../types";

import { Link } from "@tanstack/react-router";
import { EditIcon, MoreHorizontalIcon, Trash2Icon, ViewIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import CategoryIcon from "~/features/categories/components/category-icon";

import { useCategoryActions } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoriesListItem({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions(category);

  return (
    <div className="flex items-center gap-4 rounded-lg bg-white py-4 ps-6 pe-2">
      <div className="flex flex-grow items-center gap-4">
        <CategoryIcon category={category} className="size-5" />
        <Link to="/categories/$categoryId" params={{ categoryId: category.id }} className="link text-sm/5">
          {category.name}
        </Link>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <Link to="/categories/$categoryId" params={{ categoryId: category.id }}>
              <ViewIcon /> <span className="inline-block pt-0.5">Category details</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={editCategory}>
            <EditIcon /> <span className="inline-block pt-0.5">Edit category</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={deleteCategory} variant="destructive">
            <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
