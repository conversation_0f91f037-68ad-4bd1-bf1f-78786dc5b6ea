import type { CategoryData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputColor from "~/components/inputs/input-color";
import InputSwitch from "~/components/inputs/input-switch";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useCategoryCreate } from "../hooks";
import { CategoryRequestSchema } from "../schemas";
import useCategoriesStore from "../store";

interface Props {
  initialValues?: Partial<CategoryData>;
}

export default function CategoryFormCreate({ initialValues }: Props) {
  const closeDialog = useCategoriesStore(useShallow((state) => state.closeDialog));

  const form = useForm<CategoryData>({
    resolver: zodResolver(CategoryRequestSchema),
    defaultValues: {
      name: initialValues?.name ?? "",
      color: initialValues?.color ?? "#6366f1",
      is_expense: initialValues?.is_expense ?? true,
    },
  });

  const { mutate: createCategory, isPending, error } = useCategoryCreate();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => createCategory(data))} className="space-y-4">
        {error && <ErrorMessage title="Failed to create category" error={error} />}

        <InputText
          control={form.control}
          name="name"
          label="Name"
          placeholder="e.g. Groceries, Rent, Salary"
          autoFocus
        />

        <InputColor control={form.control} name="color" label="Color" />

        <InputSwitch
          control={form.control}
          name="is_expense"
          label="Is expense?"
          description="Uncheck it to mark category as income"
        />

        <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="animate-spin" /> : "Create category"}
          </Button>
          <Button type="button" variant="outline" disabled={isPending} onClick={() => closeDialog()}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
