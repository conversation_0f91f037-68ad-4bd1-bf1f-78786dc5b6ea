import type { Category } from "../types";

import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useCategoryDelete } from "../hooks";
import useCategoriesStore from "../store";

interface Props {
  category: Category;
}

export default function CategoryFormDelete({ category }: Props) {
  const closeDialog = useCategoriesStore(useShallow((state) => state.closeDialog));

  const { mutate: deleteCategory, isPending, error } = useCategoryDelete(category.id);

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-800">
        Are you sure you want to delete the <strong>{category.name}</strong> category. This action cannot be undone.
      </p>

      {error && <ErrorMessage title="Failed to delete category" error={error} />}

      <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
        <Button type="button" disabled={isPending} onClick={() => deleteCategory()}>
          {isPending ? <LoaderIcon className="animate-spin" /> : "Yes, delete account"}
        </Button>
        <Button type="button" variant="outline" disabled={isPending} onClick={closeDialog}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
