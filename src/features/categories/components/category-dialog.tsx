import { useShallow } from "zustand/react/shallow";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import useCategoriesStore from "../store";
import CategoryFormCreate from "./category-form-create";
import CategoryFormDelete from "./category-form-delete";
import CategoryFormEdit from "./category-form-edit";

export default function CategoryDialog() {
  const { dialogOpen, dialogMode, currentCategory, initialValues, closeDialog } = useCategoriesStore(
    useShallow((state) => ({ ...state }))
  );

  return (
    <Dialog open={dialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {dialogMode == "create" && "Create Category"}
            {dialogMode == "edit" && "Edit Category"}
            {dialogMode == "delete" && "Delete Category"}
          </DialogTitle>
          <DialogDescription>
            {dialogMode == "create" && "Create a new category for tracking your income and expenses."}
            {dialogMode == "edit" && (
              <>
                Edit <em className="font-medium italic">{currentCategory!.name}</em> category
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <main>
          {dialogMode == "create" && <CategoryFormCreate initialValues={initialValues} />}
          {dialogMode == "edit" && <CategoryFormEdit category={currentCategory!} />}
          {dialogMode == "delete" && <CategoryFormDelete category={currentCategory!} />}
        </main>
      </DialogContent>
    </Dialog>
  );
}
