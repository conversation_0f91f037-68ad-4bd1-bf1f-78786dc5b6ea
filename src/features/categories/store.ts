import type { Category, CategoryData } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete";

interface State {
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentCategory?: Category;
  initialValues?: Partial<CategoryData>;
}

interface Actions {
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentCategory: (category?: Category) => void;
  setInitialValues: (initialValues?: Partial<CategoryData>) => void;
  closeDialog: () => void;
}

const useCategoriesStore = create<State & Actions>()(
  immer((set) => ({
    dialogOpen: false,
    dialogMode: "create",

    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentCategory: (category) => {
      set({ currentCategory: category });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentCategory: undefined, initialValues: undefined });
    },
  }))
);

export default useCategoriesStore;
