import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useCategoriesStore from "../store";

export function useCategoryDelete(categoryId: string) {
  const queryClient = useQueryClient();
  const closeDialog = useCategoriesStore(useShallow((state) => state.closeDialog));
  const navigate = useNavigate();

  return useMutation({
    mutationFn: () => apiClient.delete(`/v1/categories/${categoryId}`),
    onSuccess: async () => {
      queryClient.removeQueries({ queryKey: ["categories", { id: categoryId }] });
      await queryClient.invalidateQueries({ queryKey: ["categories"] });

      toast.success("Category deleted", { description: `Category deleted successfully.` });

      closeDialog();

      return navigate({ to: "/categories" });
    },
  });
}
