import type { ApiError } from "~/api/client";
import type { Category } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useCategory(categoryId: string) {
  return useQuery<Category, ApiError>({
    queryFn: () => apiClient.get(`/v1/categories/${categoryId}`),
    queryKey: ["categories", { id: categoryId }],
    staleTime: 60 * 1000, // 1 minute
    enabled: !!categoryId,
  });
}
