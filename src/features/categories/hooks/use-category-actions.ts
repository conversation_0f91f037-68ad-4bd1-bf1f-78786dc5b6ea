import type { Category, CategoryData } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import useCategoriesStore from "../store";

export function useCategoryActions(category?: Category) {
  const { setDialogOpen, setDialogMode, setCurrentCategory, setInitialValues } = useCategoriesStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentCategory: state.setCurrentCategory,
      setInitialValues: state.setInitialValues,
    }))
  );

  const createCategory = useCallback(
    (initialValues?: Partial<CategoryData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editCategory = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentCategory(category);
  }, [setDialogOpen, setDialogMode, setCurrentCategory, category]);

  const deleteCategory = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentCategory(category);
  }, [setDialogOpen, setDialogMode, setCurrentCategory, category]);

  return { createCategory, editCategory, deleteCategory };
}
