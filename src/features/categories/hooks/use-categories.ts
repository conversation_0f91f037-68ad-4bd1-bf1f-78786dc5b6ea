import type { Category } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useCategories() {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Category[]>("/v1/categories"),
    queryKey: ["categories"],
    staleTime: 60 * 1000, // 1 minute
  });

  const categories = useMemo(() => data ?? [], [data]);
  const expenseCategories = useMemo(() => categories.filter((category) => category.is_expense), [categories]);
  const incomeCategories = useMemo(() => categories.filter((category) => !category.is_expense), [categories]);

  return { categories, expenseCategories, incomeCategories, isFetching, isLoading, error };
}
