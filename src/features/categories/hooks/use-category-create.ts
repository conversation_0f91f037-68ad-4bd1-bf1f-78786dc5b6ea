import type { ApiError } from "~/api/client";
import type { Category, CategoryData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useCategoriesStore from "../store";

export function useCategoryCreate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useCategoriesStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Category, ApiError, CategoryData>({
    mutationFn: (data) => apiClient.post("/v1/categories", data),
    onSuccess: async (category) => {
      await queryClient.invalidateQueries({ queryKey: ["categories"] });

      toast.success("Category created", { description: `Category ${category.name} created successfully.` });

      setDialogOpen(false);
    },
  });
}
