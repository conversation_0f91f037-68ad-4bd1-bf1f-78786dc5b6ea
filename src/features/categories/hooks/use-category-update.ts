import type { ApiError } from "~/api/client";
import type { Category, CategoryData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useCategoriesStore from "../store";

export function useCategoryUpdate(categoryId: string) {
  const queryClient = useQueryClient();
  const closeDialog = useCategoriesStore(useShallow((state) => state.closeDialog));

  return useMutation<Category, ApiError, CategoryData>({
    mutationFn: (data) => apiClient.patch(`/v1/categories/${categoryId}`, data),
    onSuccess: async (category) => {
      await queryClient.invalidateQueries({ queryKey: ["categories"] });

      toast.success("Category updated", { description: `Changes to category ${category.name} saved successfully.` });

      closeDialog();
    },
  });
}
