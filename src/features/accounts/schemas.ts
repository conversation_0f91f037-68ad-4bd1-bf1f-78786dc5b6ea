import { z } from "zod";

import { currencies } from "~/lib/currencies";
import { decimal, hexColor, nullableString } from "~/lib/schemas";

export const accountTypes = ["cash", "card", "bank_account", "savings", "loan", "investment", "other"] as const;

export const accountTypesMap: Record<(typeof accountTypes)[number], string> = {
  cash: "Cash",
  card: "Card",
  bank_account: "Bank Account",
  savings: "Savings",
  loan: "Loan",
  investment: "Investment",
  other: "Other",
};

export const AccountCreateSchema = z.object({
  name: z.string().min(3).max(50),
  account_type: z.enum(accountTypes),
  group: nullableString(),
  currency: z.enum(currencies),
  opening_balance: decimal(),
  overdraft_limit: decimal(),
  color: hexColor(),
  description: nullableString(),
});

export const AccountUpdateSchema = z.object({
  ...AccountCreateSchema.omit({ opening_balance: true, currency: true }).shape,
  is_active: z.boolean(),
});
