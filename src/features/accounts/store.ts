import type { Account, AccountCreateData } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete" | "import";

interface State {
  expandGroups: boolean;
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentAccount?: Account;
  initialValues?: Partial<AccountCreateData>;
}

interface Actions {
  toggleExpandGroups: () => void;
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentAccount: (account?: Account) => void;
  setInitialValues: (initialValues?: Partial<Account>) => void;
  closeDialog: () => void;
}

const useAccountsStore = create<State & Actions>()(
  immer((set) => ({
    expandGroups: true,
    dialogOpen: false,
    dialogMode: "create",

    toggleExpandGroups: () => {
      set((state) => {
        state.expandGroups = !state.expandGroups;
      });
    },
    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentAccount: (account) => {
      set({ currentAccount: account });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentAccount: undefined, initialValues: undefined });
    },
  }))
);

export default useAccountsStore;
