import type { ApiError } from "~/api/client";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { apiClient } from "~/api";

interface ImportResponse {
  imported: number;
  errors: string[];
}

/**
 * Hook for importing accounts from CSV
 * @returns Functions and state for importing accounts
 */
export function useAccountImport() {
  const queryClient = useQueryClient();

  return useMutation<ImportResponse, ApiError, File>({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append("file", file);

      return apiClient.post("/v1/accounts/import", formData);
    },
    onSuccess: () => {
      // Invalidate `accounts` query to refresh the list
      return queryClient.invalidateQueries({ queryKey: ["accounts"] });
    },
  });
}
