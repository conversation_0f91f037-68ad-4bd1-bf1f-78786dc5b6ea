import type { ApiError } from "~/api/client";
import type { Account, AccountUpdateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useAccountsStore from "../store";

export function useAccountUpdate(accountId: string) {
  const queryClient = useQueryClient();
  const closeDialog = useAccountsStore(useShallow((state) => state.closeDialog));

  return useMutation<Account, ApiError, AccountUpdateData>({
    mutationFn: (data) => apiClient.patch(`/v1/accounts/${accountId}`, data),
    onSuccess: (account) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
      ]);

      toast.success("Account updated", { description: `Changes to account ${account.name} saved successfully.` });

      closeDialog();
    },
  });
}
