import { useState } from "react";

/**
 * Hook for exporting accounts to CSV
 * @returns Functions and state for exporting accounts
 */
export function useAccountExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const exportAccounts = async () => {
    setIsExporting(true);
    setError(null);

    try {
      // Use fetch directly to handle file download
      const response = await fetch("/api/v1/accounts/export", { method: "GET" });

      if (!response.ok) {
        const errorData = (await response.json()) as { message?: string };
        throw new Error(errorData.message || "Failed to export accounts");
      }

      // Get filename from the Content-Disposition header or use default
      const contentDisposition = response.headers.get("Content-Disposition");
      const filename = contentDisposition ? contentDisposition.split("filename=")[1].replace(/"/g, "") : "accounts.csv";

      // Create a blob from the response
      const blob = await response.blob();

      // Create a download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsExporting(false);
    }
  };

  return { exportAccounts, isExporting, error };
}
