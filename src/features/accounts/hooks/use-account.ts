import type { ApiError } from "~/api/client";
import type { Account } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useAccount(accountId: string) {
  return useQuery<Account, ApiError>({
    queryFn: () => apiClient.get(`/v1/accounts/${accountId}`),
    queryKey: ["accounts", { id: accountId }],
    staleTime: 60 * 1000, // 1 minute
    enabled: !!accountId,
  });
}
