import type { Account, AccountCreateData } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import useAccountsStore from "../store";
import { useAccountExport } from "./use-account-export";

export function useAccountActions(account?: Account) {
  const { setDialogOpen, setDialogMode, setCurrentAccount, setInitialValues, toggleExpandGroups } = useAccountsStore(
    useShallow((state) => ({
      toggleExpandGroups: state.toggleExpandGroups,
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentAccount: state.setCurrentAccount,
      setInitialValues: state.setInitialValues,
    }))
  );

  const { exportAccounts } = useAccountExport();

  const createAccount = useCallback(
    (initialValues?: Partial<AccountCreateData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editAccount = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentAccount(account);
  }, [setDialogOpen, setDialogMode, setCurrentAccount, account]);

  const deleteAccount = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentAccount(account);
  }, [setDialogOpen, setDialogMode, setCurrentAccount, account]);

  const importAccounts = useCallback(() => {
    setDialogMode("import");
    setDialogOpen(true);
  }, [setDialogOpen, setDialogMode]);

  return { createAccount, editAccount, deleteAccount, importAccounts, exportAccounts, toggleExpandGroups };
}
