import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useAccountsStore from "../store";

export function useAccountDelete(accountId: string) {
  const queryClient = useQueryClient();
  const closeDialog = useAccountsStore(useShallow((state) => state.closeDialog));
  const navigate = useNavigate();

  return useMutation({
    mutationFn: () => apiClient.delete(`/v1/accounts/${accountId}`),
    onSuccess: () => {
      queryClient.removeQueries({ queryKey: ["accounts", { id: accountId }] });
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
      ]);

      toast.success("Account deleted", { description: `Account deleted successfully.` });

      closeDialog();

      return navigate({ to: "/accounts" });
    },
  });
}
