import type { ApiError } from "~/api/client";
import type { Account, AccountCreateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useAccountsStore from "../store";

export function useAccountCreate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useAccountsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Account, ApiError, AccountCreateData>({
    mutationFn: (data) => apiClient.post("/v1/accounts", data),
    onSuccess: (account) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
      ]);

      toast.success("Account created", { description: `Account ${account.name} created successfully.` });

      setDialogOpen(false);
    },
  });
}
