import type { AccountCreateData } from "../types";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputColor from "~/components/inputs/input-color";
import InputCurrency from "~/components/inputs/input-currency";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useBaseCurrency } from "~/features/auth/hooks";

import { useAccountCreate, useAccounts } from "../hooks";
import { AccountCreateSchema } from "../schemas";
import useAccountsStore from "../store";
import { accountTypeOptions, getGroupColor } from "../utils";

interface Props {
  initialValues?: Partial<AccountCreateData>;
}

export default function AccountFormCreate({ initialValues }: Props) {
  const baseCurrency = useBaseCurrency();
  const setDialogOpen = useAccountsStore(useShallow((state) => state.setDialogOpen));

  const { groups, accounts: existingAccounts } = useAccounts();
  const { mutate: createAccount, isPending, error } = useAccountCreate();

  const defaultValues = useMemo<AccountCreateData>(
    () => ({
      name: initialValues?.name ?? "",
      group: initialValues?.group ?? "",
      account_type: initialValues?.account_type ?? "cash",
      color: initialValues?.color ?? "#6186ff",
      currency: initialValues?.currency ?? baseCurrency,
      opening_balance: initialValues?.opening_balance ?? "0.00",
      overdraft_limit: initialValues?.overdraft_limit ?? "0.00",
      description: initialValues?.description ?? "",
    }),
    [baseCurrency, initialValues]
  );

  const form = useForm<AccountCreateData>({
    defaultValues,
    resolver: zodResolver(AccountCreateSchema),
  });
  const { watch, setValue } = form;

  const group = watch("group", defaultValues.group);

  useEffect(() => {
    setValue("color", getGroupColor(group, existingAccounts));
  }, [group, existingAccounts, setValue]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => createAccount(data))} className="space-y-4">
        {error && <ErrorMessage title="Can't create account" error={error} />}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Account name"
            placeholder="My cash"
            className="md:col-span-2"
            disabled={isPending}
            required
          />

          <InputCurrency control={form.control} name="currency" label="Currency" disabled={isPending} required />
          <InputSelect
            control={form.control}
            name="account_type"
            values={accountTypeOptions}
            disabled={isPending}
            label="Account type"
          />

          <div>
            <InputText
              control={form.control}
              name="group"
              label="Group"
              placeholder="My wallets"
              list="existing-groups"
              type="search"
              disabled={isPending}
            />
            <datalist id="existing-groups">
              {groups.map((group) => (
                <option key={group} value={group}>
                  {group}
                </option>
              ))}
            </datalist>
          </div>
          <InputColor control={form.control} name="color" label="Color" disabled={isPending} />

          <InputText
            control={form.control}
            name="opening_balance"
            label="Opening balance"
            placeholder="0.00"
            type="number"
            min={0}
            step="0.01"
            disabled={isPending}
          />
          <InputText
            control={form.control}
            name="overdraft_limit"
            label="Overdraft limit"
            placeholder="0.00"
            type="number"
            min={0}
            step="0.01"
            disabled={isPending}
          />

          <InputTextarea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Cash for everyday use"
            className="md:col-span-2"
            disabled={isPending}
          />
        </div>

        <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="animate-spin" /> : "Create account"}
          </Button>
          <Button type="button" variant="outline" disabled={isPending} onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
