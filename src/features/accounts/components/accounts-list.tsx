import type { Account } from "../types";

import { useMemo } from "react";

import AccountsListGroup from "./accounts-list-group";

interface Props {
  accounts: Account[];
}

export default function AccountsList({ accounts }: Props) {
  const groups = useMemo(
    () => [
      ...accounts
        .reduce((acc, value) => {
          if (acc.has(value.group)) {
            acc.set(value.group, [...acc.get(value.group)!, value]);
          } else {
            acc.set(value.group, [value]);
          }

          return acc;
        }, new Map<string | null, Account[]>())
        .entries(),
    ],
    [accounts]
  );

  return (
    <div className="space-y-8">
      {groups.map(([group, accounts]) => (
        <AccountsListGroup key={group ?? 0} group={group} accounts={accounts} />
      ))}
    </div>
  );
}
