import { useShallow } from "zustand/react/shallow";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import useAccountsStore from "../store";
import AccountFormCreate from "./account-form-create";
import AccountFormDelete from "./account-form-delete";
import AccountFormEdit from "./account-form-edit";
import AccountFormImport from "./account-form-import";

export default function AccountDialog() {
  const { dialogOpen, dialogMode, currentAccount, initialValues, closeDialog } = useAccountsStore(
    useShallow((state) => ({ ...state }))
  );

  return (
    <Dialog open={dialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {dialogMode == "create" && "Create Account"}
            {dialogMode == "edit" && "Edit Account"}
            {dialogMode == "delete" && "Delete Account"}
            {dialogMode == "import" && "Import Accounts"}
          </DialogTitle>
          <DialogDescription>
            {dialogMode == "create" && "Create a new financial account/asset to track your money."}
            {dialogMode == "edit" && (
              <>
                Edit <em className="font-medium italic">{currentAccount!.name}</em> account
              </>
            )}
            {dialogMode == "import" && "Import accounts from a CSV file."}
          </DialogDescription>
        </DialogHeader>

        <main>
          {dialogMode == "create" && <AccountFormCreate initialValues={initialValues} />}
          {dialogMode == "edit" && <AccountFormEdit account={currentAccount!} />}
          {dialogMode == "delete" && <AccountFormDelete account={currentAccount!} />}
          {dialogMode == "import" && <AccountFormImport />}
        </main>
      </DialogContent>
    </Dialog>
  );
}
