import type { Account } from "../types";

import { useEffect, useState } from "react";

import { ChevronDownIcon, ChevronRightIcon, MoreHorizontalIcon, PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useAccountActions } from "../hooks";
import useAccountsStore from "../store";
import AccountRow from "./accounts-list-item";

interface Props {
  group: string | null;
  accounts: Account[];
}

export default function AccountsListGroup({ accounts, group }: Props) {
  const baseCurrency = useBaseCurrency();
  const expandGroups = useAccountsStore((state) => state.expandGroups);

  const { createAccount } = useAccountActions();

  const [isOpen, setOpen] = useState(expandGroups);

  const groupColor = accounts.length > 0 ? accounts[0].color : "#ffffff";

  const totalBalance = accounts.reduce((sum, account) => {
    return sum + parseFloat(account.base_current_balance || "0");
  }, 0);

  useEffect(() => {
    setOpen(expandGroups);
  }, [expandGroups]);

  return (
    <Collapsible open={isOpen} onOpenChange={setOpen}>
      <div style={{ borderLeftColor: groupColor, borderLeftWidth: 4 }}>
        <div className="bg-card flex items-center justify-between rounded-r-lg px-2 py-3">
          <div className="flex items-center gap-1">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="icon">
                {isOpen ? <ChevronDownIcon className="size-4" /> : <ChevronRightIcon className="size-4" />}
              </Button>
            </CollapsibleTrigger>

            <div className="flex items-center gap-1">
              <div className="flex flex-col items-start">
                <h2 className="text-foreground text-xl/6 font-semibold">{group ?? "Other accounts"}</h2>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="w-16 text-left">
              <p className="text-gray text-xs/5 uppercase">Acc.</p>
              <div className="text-foreground text-xl/6 font-semibold">{accounts.length}</div>
            </div>

            <div className="w-40 text-right">
              <p className="text-gray text-xs/5 uppercase">Total balance</p>
              <div className="text-foreground text-xl/6 font-semibold">
                {formatCurrency(baseCurrency, totalBalance.toFixed(4))}
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontalIcon className="size-4" />
                  <span className="sr-only">Group actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => createAccount({ group })}>
                  <PlusIcon className="size-4" />
                  Add Account
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <CollapsibleContent>
        <div className="mt-2 space-y-1">
          {accounts.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <p>No accounts in this group</p>
              <Button variant="outline" size="sm" className="mt-2" onClick={() => createAccount({ group })}>
                <PlusIcon className="mr-2 size-4" />
                Add Account
              </Button>
            </div>
          ) : (
            accounts.map((account) => <AccountRow key={account.id} account={account} />)
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
