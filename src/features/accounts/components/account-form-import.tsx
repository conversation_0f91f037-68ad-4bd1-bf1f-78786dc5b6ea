import { useRef, useState } from "react";
import * as React from "react";

import { LoaderIcon } from "lucide-react";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

import { useAccountImport } from "../hooks";
import useAccountsStore from "../store";

export default function AccountFormImport() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const setDialogOpen = useAccountsStore(useShallow((state) => state.setDialogOpen));

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { data, mutate, error, isPending } = useAccountImport();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      toast.error("Error", { description: "Please select a CSV file to import" });
      return;
    }

    mutate(selectedFile, {
      onSuccess: () => {
        setSelectedFile(null);
      },
    });
  };

  // Show the result message if the import was successful
  if (data) {
    return (
      <div className="space-y-4">
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="text-green h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Import successful</h3>
              <div className="text-green mt-2 text-sm">
                <p>Successfully imported {data.imported} accounts.</p>
                {data.errors.length > 0 && (
                  <div className="mt-2">
                    <p className="font-semibold">Errors:</p>
                    <ul className="mt-1 list-disc pl-5">
                      {data.errors.map((err, index) => (
                        <li key={index}>{err}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && <ErrorMessage title="Import failed" error={error} />}

      <p className="text-sm text-gray-800">
        Upload a CSV file with account data. The file should have the following columns: name, description,
        account_type, currency, current_balance, is_active, group, color, overdraft_limit.
      </p>

      <div className="flex items-center gap-2">
        <Input ref={fileInputRef} type="file" accept=".csv" onChange={handleFileChange} className="flex-1" />
      </div>

      {selectedFile && (
        <p className="text-sm text-gray-500">
          Selected file: {selectedFile.name} ({Number(selectedFile.size / 1024).toFixed(3)} KB)
        </p>
      )}

      <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
        <Button type="submit" disabled={!selectedFile || isPending}>
          {isPending ? <LoaderIcon className="animate-spin" /> : "Import"}
        </Button>
        <Button variant="outline" disabled={isPending} onClick={() => setDialogOpen(false)} type="button">
          Cancel
        </Button>
      </div>
    </form>
  );
}
