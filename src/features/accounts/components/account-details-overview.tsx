import type { Account } from "../types";

import { ArchiveIcon, CheckCircleIcon as <PERSON><PERSON><PERSON>, EditIcon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import Box from "~/components/blocks/box";
import DefinitionBlock from "~/components/blocks/definition-block";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useAccountActions } from "~/features/accounts/hooks";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { getAccountTypeIcon, getAccountTypeLabel } from "../utils";

interface Props {
  account: Account;
}

export default function AccountDetailsOverview({ account }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editAccount, deleteAccount } = useAccountActions(account);

  const color = account.color || "#ffffff";
  const Icon = getAccountTypeIcon(account.account_type);

  return (
    <Box className="flex flex-col gap-6 border-t-[6px] ps-8 pe-4 pt-4 pb-8" style={{ borderColor: color }}>
      <div className="flex items-center gap-4">
        <Icon className="text-gray size-4" />
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(account.created_at)}
        </p>
        {account.is_active && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <CheckIcon className="size-4" />
            Active
          </p>
        )}
        {!account.is_active && (
          <p className="text-gray flex items-center gap-1 text-sm/5">
            <ArchiveIcon className="size-4" />
            Archived
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editAccount}>
              <EditIcon /> <span className="inline-block pt-0.5">Edit account</span>
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" onClick={deleteAccount}>
              <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{account.name}</h2>
        {account.description && <p className="mt-1 line-clamp-2 text-base text-gray-600">{account.description}</p>}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title="Current Balance">
          <p className="flex items-end gap-2">
            {formatCurrency(account.currency, account.current_balance)}
            {account.currency !== baseCurrency && (
              <span className="text-gray text-base font-normal">
                ({formatCurrency(baseCurrency, account.base_current_balance)})
              </span>
            )}
          </p>
        </DefinitionBlock>
        {account.overdraft_limit && parseFloat(account.overdraft_limit) > 0 && (
          <DefinitionBlock title="Overdraft Limit">
            {formatCurrency(account.currency, account.overdraft_limit)}
          </DefinitionBlock>
        )}
      </div>

      <div className="flex items-center gap-4">
        {account.group && <Badge size="lg">{account.group}</Badge>}
        <Badge variant="secondary" size="lg">
          {getAccountTypeLabel(account.account_type)}
        </Badge>
        <Badge variant="secondary" size="lg">
          {account.currency}
        </Badge>
      </div>
    </Box>
  );
}
