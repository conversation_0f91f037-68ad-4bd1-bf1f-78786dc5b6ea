import type { Account, AccountUpdateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputColor from "~/components/inputs/input-color";
import InputSelect from "~/components/inputs/input-select";
import InputSwitch from "~/components/inputs/input-switch";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useAccounts, useAccountUpdate } from "../hooks";
import { AccountUpdateSchema } from "../schemas";
import useAccountsStore from "../store";
import { accountTypeOptions } from "../utils";

interface Props {
  account: Account;
}

export default function AccountFormEdit({ account }: Props) {
  const setDialogOpen = useAccountsStore(useShallow((state) => state.setDialogOpen));

  const { groups } = useAccounts();
  const { mutate: updateAccount, isPending, error } = useAccountUpdate(account.id);

  const defaultValues = useMemo<AccountUpdateData>(
    () => ({
      name: account.name,
      group: account.group || "",
      account_type: account.account_type,
      color: account.color,
      overdraft_limit: account.overdraft_limit,
      description: account.description || "",
      is_active: account.is_active,
    }),
    [account]
  );

  const form = useForm<AccountUpdateData>({
    defaultValues,
    resolver: zodResolver(AccountUpdateSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => updateAccount(data))} className="space-y-4">
        {error && <ErrorMessage title="Can't update account" error={error} />}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Account name"
            placeholder="My cash"
            className="md:col-span-2"
            disabled={isPending}
            required
          />

          <InputSelect
            control={form.control}
            name="account_type"
            values={accountTypeOptions}
            disabled={isPending}
            label="Account type"
          />

          <div>
            <InputText
              control={form.control}
              name="group"
              label="Group"
              placeholder="My wallets"
              list="existing-groups"
              type="search"
              disabled={isPending}
            />
            <datalist id="existing-groups">
              {groups.map((group) => (
                <option key={group} value={group}>
                  {group}
                </option>
              ))}
            </datalist>
          </div>
          <InputColor control={form.control} name="color" label="Color" disabled={isPending} />

          <InputText
            control={form.control}
            name="overdraft_limit"
            label="Overdraft limit"
            placeholder="0.00"
            type="number"
            min={0}
            step="0.01"
            disabled={isPending}
          />

          <InputTextarea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Cash for everyday use"
            className="md:col-span-2"
            disabled={isPending}
          />

          <InputSwitch
            control={form.control}
            name="is_active"
            label="Is active?"
            disabled={isPending}
            description="Uncheck to archive this account"
            className="md:col-span-2"
          />
        </div>

        <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="animate-spin" /> : "Save changes"}
          </Button>
          <Button type="button" variant="outline" disabled={isPending} onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
