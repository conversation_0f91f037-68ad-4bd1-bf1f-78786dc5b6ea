import type { Account } from "../types";

import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useAccountDelete } from "../hooks";
import useAccountsStore from "../store";

interface Props {
  account: Account;
}

export default function AccountFormDelete({ account }: Props) {
  const closeDialog = useAccountsStore(useShallow((state) => state.closeDialog));

  const { mutate: deleteAccount, isPending, error } = useAccountDelete(account.id);

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-800">
        Are you sure you want to delete <strong>{account.name}</strong> account? This action cannot be undone. All
        related transactions will be deleted as well.
      </p>

      {error && <ErrorMessage title="Can't delete account" error={error} />}

      <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
        <Button type="button" disabled={isPending} onClick={() => deleteAccount()}>
          {isPending ? <LoaderIcon className="animate-spin" /> : "Yes, delete account"}
        </Button>
        <Button type="button" variant="outline" disabled={isPending} onClick={closeDialog}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
