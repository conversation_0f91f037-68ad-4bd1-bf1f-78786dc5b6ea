import type { Currency } from "~/lib/currencies";

import { z } from "zod";

import { AccountCreateSchema, accountTypes, AccountUpdateSchema } from "./schemas";

export type AccountType = (typeof accountTypes)[number];

export interface Account {
  id: string;
  account_type: AccountType;
  name: string;
  group: string | null;
  currency: Currency;
  current_balance: string;
  opening_balance: string;
  overdraft_limit: string;
  is_active: boolean;
  color: string;
  description: string | null;
  base_opening_balance: string;
  base_current_balance: string;
  created_at: string;
  updated_at: string;
}

export type AccountCreateData = z.infer<typeof AccountCreateSchema>;
export type AccountUpdateData = z.infer<typeof AccountUpdateSchema>;
