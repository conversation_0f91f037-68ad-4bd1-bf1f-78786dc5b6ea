import type { LucideIcon } from "lucide-react";
import type { Account, AccountType } from "./types";

import { Building2, CircleDot, CreditCard, PercentCircle, PiggyBank, TrendingDown, Wallet } from "lucide-react";

import { accountTypesMap } from "./schemas";

/**
 * Maps account types to their corresponding Lucide React icons
 */
export const ACCOUNT_TYPE_ICONS: Record<AccountType, LucideIcon> = {
  cash: Wallet,
  card: CreditCard,
  bank_account: Building2,
  savings: PiggyBank,
  loan: TrendingDown,
  investment: PercentCircle,
  other: CircleDot,
};

export const accountTypeOptions = Object.entries(accountTypesMap).map(([key, value]) => ({ value: key, label: value }));

/**
 * Gets the appropriate icon component for an account type
 */
export function getAccountTypeIcon(type: AccountType): LucideIcon {
  return ACCOUNT_TYPE_ICONS[type];
}

/**
 * Gets a human-readable label for an account type
 */
export function getAccountTypeLabel(type: AccountType): string {
  return accountTypesMap[type];
}

/**
 * Retrieves the color associated with a specified group from the list of accounts.
 *
 * @param {string|null} group - The group identifier, can be a string or null.
 * @param {Account[]} accounts - An array of accounts containing group and color information.
 * @return {string} The color associated with the specified group, or a default color if not found.
 */
export function getGroupColor(group: string | null, accounts: Account[]) {
  return accounts.find((account) => account.group === (group && group.length ? group : null))?.color ?? "#6186ff";
}

/**
 * Retrieves the account name, including the group if it exists.
 *
 * @param {Account} account - The account object containing the name and optional group information.
 * @return {string} The formatted account name. If a group exists, it returns the group concatenated with the name. Otherwise, it returns the name alone.
 */
export function getAccountName(account: Account) {
  return account.group ? `${account.group} - ${account.name}` : account.name;
}
