import type { ConfirmationOptions } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

interface State {
  isOpen: boolean;
  options: ConfirmationOptions | null;
  resolve: ((value: boolean) => void) | null;
}

interface Actions {
  confirm: (options: ConfirmationOptions) => Promise<boolean>;
  handleConfirm: () => void;
  handleCancel: () => void;
  reset: () => void;
}

const useConfirmationsStore = create<State & Actions>()(
  immer((set) => ({
    isOpen: false,
    options: null,
    resolve: null,

    confirm: async (options) => {
      return new Promise((resolve) => {
        set({ isOpen: true, options, resolve });
      });
    },
    handleConfirm: () => {
      set((state) => {
        state.resolve?.(true);
        state.reset();
      });
    },
    handleCancel: () => {
      set((state) => {
        state.resolve?.(false);
        state.reset();
      });
    },
    reset: () => {
      set({ isOpen: false, options: null, resolve: null });
    },
  }))
);

export default useConfirmationsStore;
