# Confirmations Feature

This feature provides a simple way to display confirmation dialogs in your application using the `useConfirm` hook.

## Overview

The confirmations feature allows you to easily prompt users for confirmation before performing actions. It uses a Promise-based API that makes it simple to integrate into your application's flow.

## Installation

The feature is already integrated into the application. The `ConfirmationDialog` component is rendered in the main App component, so you don't need to add it to your components.

## Usage

### Basic Usage

```tsx
import { useConfirm } from "~/features/ui/confirmations/hooks";

function MyComponent() {
  const confirm = useConfirm();

  const handleDeleteClick = async () => {
    const confirmed = await confirm({
      title: "Delete Item",
      description: "Are you sure you want to delete this item? This action cannot be undone.",
      confirmText: "Delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      // User confirmed the action
      deleteItem();
    } else {
      // User canceled the action
    }
  };

  return <button onClick={handleDeleteClick}>Delete</button>;
}
```

### Simplified Usage

For simple confirmations, you can omit optional properties:

```tsx
const confirmed = await confirm({
  title: "Save Changes",
  description: "Do you want to save your changes?",
});

if (confirmed) {
  saveChanges();
}
```

## API

### `useConfirm` Hook

The `useConfirm` hook returns a function that displays a confirmation dialog and returns a Promise that resolves to a boolean.

#### Parameters

The confirm function accepts a `ConfirmationOptions` object with the following properties:

| Property | Type | Required | Default | Description |
|----------|------|----------|---------|-------------|
| title | string | Yes | - | The title of the confirmation dialog |
| description | React.ReactNode | No | - | The description or content of the dialog |
| confirmText | string | No | "Confirm" | The text for the confirm button |
| cancelText | string | No | "Cancel" | The text for the cancel button |
| variant | "default" \| "destructive" | No | "default" | The visual style of the dialog |

#### Return Value

The confirm function returns a Promise that resolves to:
- `true` if the user confirms the action
- `false` if the user cancels the action

## Examples

### Confirmation Before Deletion

```tsx
const handleDelete = async (id) => {
  const confirmed = await confirm({
    title: "Delete Account",
    description: "Are you sure you want to delete this account? All associated data will be permanently removed.",
    confirmText: "Delete",
    cancelText: "Keep",
    variant: "destructive",
  });

  if (confirmed) {
    await deleteAccount(id);
    toast.success("Account deleted successfully");
  }
};
```

### Confirmation Before Navigation

```tsx
const handleNavigateAway = async () => {
  if (hasUnsavedChanges) {
    const confirmed = await confirm({
      title: "Unsaved Changes",
      description: "You have unsaved changes. Are you sure you want to leave this page?",
      confirmText: "Leave",
      cancelText: "Stay",
    });

    if (confirmed) {
      navigate("/next-page");
    }
  } else {
    navigate("/next-page");
  }
};
```

### Confirmation with Custom Content

```tsx
const handleImportant = async () => {
  const confirmed = await confirm({
    title: "Important Action",
    description: (
      <div>
        <p>This action will:</p>
        <ul className="list-disc pl-5 mt-2">
          <li>Update your profile</li>
          <li>Send notifications to your contacts</li>
          <li>Change your account settings</li>
        </ul>
      </div>
    ),
    confirmText: "Proceed",
  });

  if (confirmed) {
    performImportantAction();
  }
};
```

## Implementation Details

The confirmation feature uses:
- Zustand for state management
- React's Promise API for async/await support
- AlertDialog component from the UI library

This implementation ensures that confirmations are handled in a clean, non-blocking way that integrates well with async operations.
