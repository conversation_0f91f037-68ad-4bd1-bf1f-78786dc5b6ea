import type { Goal } from "../types";

import { EditIcon, TrashIcon } from "lucide-react";

import DefinitionBlock from "~/components/blocks/definition-block";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useGoalActions } from "../hooks";

interface Props {
  goal: Goal;
}

export default function GoalDetailsOverview({ goal }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editGoal, deleteGoal } = useGoalActions(goal);

  const currentAmount = Number(goal.current_amount);
  const targetAmount = goal.target_amount ? Number(goal.target_amount) : null;
  const progressPercentage = targetAmount ? Math.min((currentAmount / targetAmount) * 100, 100) : 0;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Goal Header */}
      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-6 w-6 rounded-full" style={{ backgroundColor: goal.color }} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{goal.name}</h2>
              {goal.description && <p className="mt-1 text-gray-600">{goal.description}</p>}
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={editGoal}>
              <EditIcon className="mr-2 size-4" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={deleteGoal}>
              <TrashIcon className="mr-2 size-4" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Progress Section */}
      {targetAmount && (
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold">Progress</h3>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Current Progress</span>
              <span className="font-medium">{progressPercentage.toFixed(1)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{formatCurrency(baseCurrency, goal.current_amount)}</span>
              <span>{formatCurrency(baseCurrency, goal.target_amount!)}</span>
            </div>
            {targetAmount > currentAmount && (
              <p className="text-sm text-gray-600">
                {formatCurrency(baseCurrency, (targetAmount - currentAmount).toString())} remaining to reach your goal
              </p>
            )}
          </div>
        </div>
      )}

      {/* Goal Details */}
      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <h3 className="mb-4 text-lg font-semibold">Details</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <DefinitionBlock title="Current Amount">{formatCurrency(baseCurrency, goal.current_amount)}</DefinitionBlock>
          <DefinitionBlock title="Target Amount">
            {targetAmount ? formatCurrency(baseCurrency, goal.target_amount!) : "Not set"}
          </DefinitionBlock>
          <DefinitionBlock title="Target Date">{formatDate(goal.target_date)}</DefinitionBlock>
          <DefinitionBlock title="Status">{goal.is_active ? "Active" : "Inactive"}</DefinitionBlock>
          <DefinitionBlock title="Created">{formatDateTime(goal.created_at)}</DefinitionBlock>
          <DefinitionBlock title="Last Updated">{formatDateTime(goal.updated_at)}</DefinitionBlock>
        </div>
      </div>
    </div>
  );
}
