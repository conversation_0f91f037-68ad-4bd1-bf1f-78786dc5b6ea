import type { Goal } from "../types";

import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useGoalDelete } from "../hooks";
import useGoalsStore from "../store";

interface Props {
  goal: Goal;
}

export default function GoalFormDelete({ goal }: Props) {
  const setDialogOpen = useGoalsStore(useShallow((state) => state.setDialogOpen));

  const { mutate: deleteGoal, isPending, error } = useGoalDelete(goal.id);

  return (
    <div className="space-y-4">
      {error && <ErrorMessage title="Failed to delete goal" error={error} />}

      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Are you sure you want to delete the goal <strong>{goal.name}</strong>?
        </p>
        <p className="text-sm text-gray-500">This action cannot be undone.</p>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
          Cancel
        </Button>
        <Button type="button" variant="destructive" disabled={isPending} onClick={() => deleteGoal()}>
          {isPending ? "Deleting..." : "Delete Goal"}
        </Button>
      </div>
    </div>
  );
}
