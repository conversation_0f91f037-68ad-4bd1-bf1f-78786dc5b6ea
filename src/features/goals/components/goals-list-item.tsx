import type { Goal } from "../types";

import { Link } from "@tanstack/react-router";
import { EditIcon, MoreHorizontalIcon, TrashIcon, ViewIcon } from "lucide-react";

import DefinitionBlock from "~/components/blocks/definition-block";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useGoalActions } from "../hooks";

interface Props {
  goal: Goal;
}

export default function GoalsListItem({ goal }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editGoal, deleteGoal } = useGoalActions(goal);

  const currentAmount = Number(goal.current_amount);
  const targetAmount = goal.target_amount ? Number(goal.target_amount) : null;
  const progressPercentage = targetAmount ? Math.min((currentAmount / targetAmount) * 100, 100) : 0;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-4">
          <div className="flex items-center gap-3">
            <div className="h-4 w-4 rounded-full" style={{ backgroundColor: goal.color }} />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{goal.name}</h3>
              {goal.description && <p className="text-sm text-gray-500">{goal.description}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <DefinitionBlock title="Current Amount">
              {formatCurrency(baseCurrency, goal.current_amount)}
            </DefinitionBlock>
            {targetAmount && (
              <DefinitionBlock title="Target Amount">
                {formatCurrency(baseCurrency, goal.target_amount!)}
              </DefinitionBlock>
            )}
            {goal.target_date && <DefinitionBlock title="Target Date">{formatDate(goal.target_date)}</DefinitionBlock>}
            <DefinitionBlock title="Status">{goal.is_active ? "Active" : "Inactive"}</DefinitionBlock>
          </div>

          {targetAmount && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{progressPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>
          )}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontalIcon className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link to="/goals/$goalId" params={{ goalId: goal.id }}>
                <ViewIcon className="mr-2 size-4" />
                View Details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={editGoal}>
              <EditIcon className="mr-2 size-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={deleteGoal}>
              <TrashIcon className="mr-2 size-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
