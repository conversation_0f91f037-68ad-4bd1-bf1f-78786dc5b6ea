import { useShallow } from "zustand/react/shallow";

import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "~/components/ui/dialog";

import useGoalsStore from "../store";
import GoalFormCreate from "./goal-form-create";
import GoalFormDelete from "./goal-form-delete";
import GoalFormEdit from "./goal-form-edit";

export default function GoalDialog() {
  const { dialogOpen, dialogMode, currentGoal, initialValues, closeDialog } = useGoalsStore(
    useShallow((state) => ({
      dialogOpen: state.dialogOpen,
      dialogMode: state.dialogMode,
      currentGoal: state.currentGoal,
      initialValues: state.initialValues,
      closeDialog: state.closeDialog,
    }))
  );

  const getDialogTitle = () => {
    switch (dialogMode) {
      case "create":
        return "Create Goal";
      case "edit":
        return "Edit Goal";
      case "delete":
        return "Delete Goal";
      default:
        return "Goal";
    }
  };

  const renderDialogContent = () => {
    switch (dialogMode) {
      case "create":
        return <GoalFormCreate initialValues={initialValues} />;
      case "edit":
        return currentGoal ? <GoalFormEdit goal={currentGoal} /> : null;
      case "delete":
        return currentGoal ? <GoalFormDelete goal={currentGoal} /> : null;
      default:
        return null;
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
        </DialogHeader>

        {renderDialogContent()}
      </DialogContent>
    </Dialog>
  );
}
