import type { Goal, GoalUpdateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputColor from "~/components/inputs/input-color";
import InputDate from "~/components/inputs/input-date";
import InputSwitch from "~/components/inputs/input-switch";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useGoalUpdate } from "../hooks";
import { GoalUpdateSchema } from "../schemas";
import useGoalsStore from "../store";

interface Props {
  goal: Goal;
}

export default function GoalFormEdit({ goal }: Props) {
  const setDialogOpen = useGoalsStore(useShallow((state) => state.setDialogOpen));

  const { mutate: updateGoal, isPending, error } = useGoalUpdate(goal.id);

  const defaultValues = useMemo<GoalUpdateData>(
    () => ({
      name: goal.name,
      description: goal.description || "",
      color: goal.color,
      target_amount: goal.target_amount || "",
      target_date: goal.target_date || "",
      is_active: goal.is_active,
    }),
    [goal]
  );

  const form = useForm<GoalUpdateData>({
    defaultValues,
    resolver: zodResolver(GoalUpdateSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => updateGoal(data))} className="space-y-4">
        {error && <ErrorMessage title="Failed to update goal" error={error} />}

        <InputText
          control={form.control}
          name="name"
          label="Name"
          placeholder="e.g. Emergency Fund, Vacation, New Car"
          autoFocus
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          placeholder="Optional description for your goal"
        />

        <InputColor control={form.control} name="color" label="Color" />

        <InputText
          control={form.control}
          name="target_amount"
          label="Target Amount"
          placeholder="e.g. 5000.00"
          hint="Optional"
        />

        <InputDate control={form.control} name="target_date" label="Target Date" hint="Optional" />

        <InputSwitch
          control={form.control}
          name="is_active"
          label="Active"
          description="Inactive goals are hidden by default"
        />

        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Updating..." : "Update Goal"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
