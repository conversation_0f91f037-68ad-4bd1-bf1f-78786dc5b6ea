import type { GoalCreateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputColor from "~/components/inputs/input-color";
import InputDate from "~/components/inputs/input-date";
import InputMultiSelect from "~/components/inputs/input-multi-select";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";

import { useGoalCreate } from "../hooks";
import { GoalCreateSchema } from "../schemas";
import useGoalsStore from "../store";

interface Props {
  initialValues?: Partial<GoalCreateData>;
}

export default function GoalFormCreate({ initialValues }: Props) {
  const closeDialog = useGoalsStore(useShallow((state) => state.closeDialog));
  const { activeAccounts } = useAccounts();

  const accountOptions = useMemo(
    () =>
      activeAccounts.map((account) => ({
        label: account.name,
        value: account.id,
      })),
    [activeAccounts]
  );

  const form = useForm<GoalCreateData>({
    resolver: zodResolver(GoalCreateSchema),
    defaultValues: {
      name: initialValues?.name ?? "",
      description: initialValues?.description ?? "",
      color: initialValues?.color ?? "#6366f1",
      target_amount: initialValues?.target_amount ?? "",
      target_date: initialValues?.target_date ?? "",
      account_ids: initialValues?.account_ids ?? [],
    },
  });

  const { mutate: createGoal, isPending, error } = useGoalCreate();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => createGoal(data))} className="space-y-4">
        {error && <ErrorMessage title="Failed to create goal" error={error} />}

        <InputText
          control={form.control}
          name="name"
          label="Name"
          placeholder="e.g. Emergency Fund, Vacation, New Car"
          autoFocus
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          placeholder="Optional description for your goal"
        />

        <InputColor control={form.control} name="color" label="Color" />

        <InputText
          control={form.control}
          name="target_amount"
          label="Target Amount"
          placeholder="e.g. 5000.00"
          hint="Optional"
        />

        <InputDate control={form.control} name="target_date" label="Target Date" hint="Optional" />

        <InputMultiSelect
          control={form.control}
          name="account_ids"
          label="Accounts"
          options={accountOptions}
          placeholder="Select accounts to track this goal"
        />

        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={closeDialog}>
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Creating..." : "Create Goal"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
