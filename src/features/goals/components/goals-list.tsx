import type { Goal } from "../types";

import { PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";

import { useGoalActions } from "../hooks";
import GoalsListItem from "./goals-list-item";

interface Props {
  goals: Goal[];
}

export default function GoalsList({ goals }: Props) {
  const { createGoal } = useGoalActions();

  if (goals.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 p-12 text-center">
        <h3 className="text-lg font-semibold text-gray-900">No goals found</h3>
        <p className="mt-2 text-sm text-gray-500">Get started by creating your first goal.</p>
        <Button className="mt-4" onClick={() => createGoal()}>
          <PlusIcon className="mr-2 size-4" />
          Create Goal
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {goals.map((goal) => (
        <GoalsListItem key={goal.id} goal={goal} />
      ))}
    </div>
  );
}
