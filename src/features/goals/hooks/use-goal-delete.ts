import type { ApiError } from "~/api/client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useGoalsStore from "../store";

export function useGoalDelete(goalId: string) {
  const queryClient = useQueryClient();

  const setDialogOpen = useGoalsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<void, ApiError, void>({
    mutationFn: () => apiClient.delete(`/v1/goals/${goalId}`),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ["goals"] });

      toast.success("Goal deleted", { description: "Goal deleted successfully." });

      setDialogOpen(false);
    },
  });
}
