import type { Goal } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useGoals() {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Goal[]>("/v1/goals"),
    queryKey: ["goals"],
    staleTime: 60 * 1000, // 1 minute
  });

  const goals = useMemo(() => data ?? [], [data]);
  const activeGoals = useMemo(() => goals.filter((goal) => goal.is_active), [goals]);
  const inactiveGoals = useMemo(() => goals.filter((goal) => !goal.is_active), [goals]);

  return { goals, activeGoals, inactiveGoals, isFetching, isLoading, error };
}
