import type { ApiError } from "~/api/client";
import type { Goal, GoalCreateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useGoalsStore from "../store";

export function useGoalCreate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useGoalsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Goal, ApiError, GoalCreateData>({
    mutationFn: (data) => apiClient.post("/v1/goals", data),
    onSuccess: (goal) => {
      void queryClient.invalidateQueries({ queryKey: ["goals"] });

      toast.success("Goal created", { description: `Goal ${goal.name} created successfully.` });

      setDialogOpen(false);
    },
  });
}
