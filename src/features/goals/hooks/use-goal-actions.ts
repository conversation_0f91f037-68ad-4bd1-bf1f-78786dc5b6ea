import type { Goal, GoalCreateData } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import useGoalsStore from "../store";

export function useGoalActions(goal?: Goal) {
  const { setDialogOpen, setDialogMode, setCurrentGoal, setInitialValues } = useGoalsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentGoal: state.setCurrentGoal,
      setInitialValues: state.setInitialValues,
    }))
  );

  const createGoal = useCallback(
    (initialValues?: Partial<GoalCreateData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editGoal = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentGoal(goal);
  }, [setDialogOpen, setDialogMode, setCurrentGoal, goal]);

  const deleteGoal = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentGoal(goal);
  }, [setDialogOpen, setDialogMode, setCurrentGoal, goal]);

  return { createGoal, editGoal, deleteGoal };
}
