import type { Goal, GoalCreateData } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete";

interface State {
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentGoal?: Goal;
  initialValues?: Partial<GoalCreateData>;
}

interface Actions {
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentGoal: (goal?: Goal) => void;
  setInitialValues: (initialValues?: Partial<GoalCreateData>) => void;
  closeDialog: () => void;
}

const useGoalsStore = create<State & Actions>()(
  immer((set) => ({
    dialogOpen: false,
    dialogMode: "create",

    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentGoal: (goal) => {
      set({ currentGoal: goal });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentGoal: undefined, initialValues: undefined });
    },
  }))
);

export default useGoalsStore;
