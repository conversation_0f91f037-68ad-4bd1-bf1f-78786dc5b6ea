import { z } from "zod";

import { decimal, hexColor, nullableString } from "~/lib/schemas";

export const GoalCreateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  color: hexColor(),
  target_amount: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || decimal().safeParse(value).success, {
      message: "Target amount must be a valid number",
    })
    .refine((value) => value === null || Number(value) >= 0.01, {
      message: "Target amount must be at least 0.01",
    }),
  target_date: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || z.string().date().safeParse(value).success, {
      message: "Target date must be a valid date",
    }),
  account_ids: z.array(z.uuid()).min(1, "At least one account must be selected"),
});

export const GoalUpdateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  color: hexColor(),
  target_amount: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || decimal().safeParse(value).success, {
      message: "Target amount must be a valid number",
    })
    .refine((value) => value === null || Number(value) >= 0.01, {
      message: "Target amount must be at least 0.01",
    }),
  target_date: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null))
    .refine((value) => value === null || z.string().date().safeParse(value).success, {
      message: "Target date must be a valid date",
    }),
  account_ids: z.array(z.uuid()).min(1, "At least one account must be selected"),
  is_active: z.boolean(),
});
