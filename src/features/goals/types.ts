import { z } from "zod";

import { GoalCreateSchema, GoalUpdateSchema } from "./schemas";

export interface Goal {
  id: string;
  name: string;
  color: string;
  description: string | null;
  target_amount: string | null;
  target_date: string | null;
  current_amount: string;
  is_active: boolean;
  account_ids: string[];
  created_at: string;
  updated_at: string;
}

export type GoalCreateData = z.infer<typeof GoalCreateSchema>;
export type GoalUpdateData = z.infer<typeof GoalUpdateSchema>;
