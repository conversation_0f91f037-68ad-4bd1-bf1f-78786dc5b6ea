import type { Transaction, TransactionData } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete";

interface State {
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentTransaction?: Transaction;
  initialValues?: Partial<TransactionData>;
}

interface Actions {
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentTransaction: (transaction?: Transaction) => void;
  setInitialValues: (initialValues?: Partial<TransactionData>) => void;
  closeDialog: () => void;
}

const useTransactionsStore = create<State & Actions>()(
  immer((set) => ({
    dialogOpen: false,
    dialogMode: "create",

    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentTransaction: (transaction) => {
      set({ currentTransaction: transaction });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentTransaction: undefined, initialValues: undefined });
    },
  }))
);

export default useTransactionsStore;
