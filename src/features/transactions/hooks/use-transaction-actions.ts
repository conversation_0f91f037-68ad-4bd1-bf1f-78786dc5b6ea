import type { Transaction, TransactionData } from "../types";

import { useCallback } from "react";

import { formatISO } from "date-fns";
import { useShallow } from "zustand/react/shallow";

import useTransactionsStore from "../store";

export function useTransactionActions(transaction?: Transaction) {
  const { setDialogOpen, setDialogMode, setCurrentTransaction, setInitialValues } = useTransactionsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentTransaction: state.setCurrentTransaction,
      setInitialValues: state.setInitialValues,
    }))
  );

  const createTransaction = useCallback(
    (initialValues?: Partial<TransactionData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editTransaction = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentTransaction(transaction);
  }, [setDialogOpen, setDialogMode, setCurrentTransaction, transaction]);

  const deleteTransaction = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentTransaction(transaction);
  }, [setDialogOpen, setDialogMode, setCurrentTransaction, transaction]);

  const copyTransaction = useCallback(() => {
    if (!transaction) return;

    // Copy all transaction data except the date (set to current date)
    const initialValues: Partial<TransactionData> = {
      transaction_type: transaction.transaction_type,
      transaction_date: formatISO(new Date(), { representation: "date" }),
      description: transaction.description || "",
      category_id: transaction.category_id || "",
      account_id: transaction.account_id,
      amount: transaction.amount,
      account_to_id: transaction.account_to_id || "",
      amount_to: transaction.amount_to,
    };

    setDialogMode("create");
    setDialogOpen(true);
    setInitialValues(initialValues);
  }, [setDialogOpen, setDialogMode, setInitialValues, transaction]);

  return { createTransaction, editTransaction, deleteTransaction, copyTransaction };
}
