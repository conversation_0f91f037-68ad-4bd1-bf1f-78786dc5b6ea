import type { TransactionsResponse } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

interface UseTransactionsParams {
  page?: number;
  per_page?: number;
}

export function useTransactions({ page = 1, per_page = 10 }: UseTransactionsParams = {}) {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<TransactionsResponse>(`/v1/transactions?page=${page}&per_page=${per_page}`),
    queryKey: ["transactions", page, per_page],
    staleTime: 60 * 1000, // 1 minute
  });

  const transactions = useMemo(() => data?.items ?? [], [data]);
  const pagination = useMemo(() => data?.pagination, [data]);

  // Group transactions by type
  const incomeTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "income"),
    [transactions]
  );

  const expenseTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "expense"),
    [transactions]
  );

  const transferTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "transfer"),
    [transactions]
  );

  return {
    transactions,
    incomeTransactions,
    expenseTransactions,
    transferTransactions,
    pagination,
    isFetching,
    isLoading,
    error
  };
}
