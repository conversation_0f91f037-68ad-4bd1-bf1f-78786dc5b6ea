import type { ApiError } from "~/api/client";
import type { Transaction, TransactionData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useTransactionsStore from "../store";

interface UpdateTransactionParams {
  id: string;
  data: TransactionData;
}

export function useTransactionUpdate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useTransactionsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Transaction, ApiError, UpdateTransactionParams>({
    mutationFn: ({ id, data }) => apiClient.patch(`/v1/transactions/${id}`, data),
    onSuccess: (transaction) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["transactions"] }),
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
        queryClient.invalidateQueries({ queryKey: ["budgets"] }),
      ]);

      toast.success("Transaction updated", {
        description: `Transaction for ${transaction.amount} updated successfully.`,
      });

      setDialogOpen(false);
    },
  });
}
