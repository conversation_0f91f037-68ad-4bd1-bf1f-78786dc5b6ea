import type { Account } from "~/features/accounts/types";
import type { Category } from "~/features/categories/types";
import type { PaginatedResponse } from "~/types";

import { z } from "zod";

import { TransactionRequestSchema } from "./schemas";

export type TransactionType = "income" | "expense" | "transfer";

export interface Transaction {
  id: string;
  transaction_type: TransactionType;
  transaction_date: string;
  description: string | null;

  category_id: string | null;
  category: Category | null;

  account_id: string;
  account: Account;
  amount: string;
  base_amount: string;

  account_to_id: string | null;
  account_to: Account | null;
  amount_to: string;
  base_amount_to: string;

  created_at: string;
  updated_at: string;
}

export type TransactionData = z.infer<typeof TransactionRequestSchema>;

export type TransactionsResponse = PaginatedResponse<Transaction>;
