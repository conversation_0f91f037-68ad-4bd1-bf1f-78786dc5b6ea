import { useCallback, useState } from "react";

import PaginationBlock from "~/components/blocks/pagination-block";
import LoadingIndicator from "~/components/elements/loading-indicator";

import { useTransactions } from "../hooks";
import TransactionsList from "./transactions-list";

export default function TransactionsView() {
  const [page, setPage] = useState(1);

  const { transactions, pagination, isLoading } = useTransactions({ page });

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage >= 1 && (!pagination || newPage <= pagination.pages)) {
        setPage(newPage);
      }
    },
    [pagination]
  );

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-4">
      <TransactionsList transactions={transactions} />

      {pagination && <PaginationBlock pagination={pagination} currentPage={page} onPageChange={handlePageChange} />}
    </div>
  );
}
