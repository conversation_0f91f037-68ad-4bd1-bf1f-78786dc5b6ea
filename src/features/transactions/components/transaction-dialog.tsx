import { useShallow } from "zustand/react/shallow";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import useTransactionsStore from "../store";
import TransactionFormCreate from "./transaction-form-create";
import TransactionFormDelete from "./transaction-form-delete";
import TransactionFormEdit from "./transaction-form-edit";

export default function TransactionDialog() {
  const { dialogOpen, dialogMode, currentTransaction, initialValues, closeDialog } = useTransactionsStore(
    useShallow((state) => ({ ...state }))
  );

  return (
    <Dialog open={dialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {dialogMode == "create" && "Create Transaction"}
            {dialogMode == "edit" && "Edit Transaction"}
            {dialogMode == "delete" && "Delete Transaction"}
          </DialogTitle>
          <DialogDescription>
            {dialogMode == "create" && "Create a new transaction to track your income, expenses, or transfers."}
            {dialogMode == "edit" && (
              <>
                Edit transaction from <em className="font-medium italic">{currentTransaction!.transaction_date}</em>
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <main>
          {dialogMode == "create" && <TransactionFormCreate initialValues={initialValues} />}
          {dialogMode == "edit" && <TransactionFormEdit transaction={currentTransaction!} />}
          {dialogMode == "delete" && <TransactionFormDelete transaction={currentTransaction!} />}
        </main>
      </DialogContent>
    </Dialog>
  );
}
