import type { Transaction } from "../types";

import { useShallow } from "zustand/react/shallow";

import { LoaderIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useTransactionDelete } from "../hooks";
import useTransactionsStore from "../store";

interface Props {
  transaction: Transaction;
}

export default function TransactionFormDelete({ transaction }: Props) {
  const setDialogOpen = useTransactionsStore(useShallow((state) => state.setDialogOpen));
  const { mutate: deleteTransaction, isPending, error } = useTransactionDelete();

  const handleDelete = () => {
    deleteTransaction(transaction.id);
  };

  return (
    <div className="space-y-4">
      {error && <ErrorMessage title="Can't delete transaction" error={error} />}

      <p className="text-muted-foreground">
        Are you sure you want to delete this transaction? This action cannot be undone.
      </p>

      <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
        <Button type="button" variant="destructive" disabled={isPending} onClick={handleDelete}>
          {isPending ? <LoaderIcon className="animate-spin" /> : "Delete transaction"}
        </Button>
        <Button type="button" variant="outline" disabled={isPending} onClick={() => setDialogOpen(false)}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
