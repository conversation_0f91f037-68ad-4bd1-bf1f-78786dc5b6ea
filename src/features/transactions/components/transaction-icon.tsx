import type { Transaction } from "../types";

import { ArrowRightLeftIcon, CircleMinusIcon, CirclePlusIcon } from "lucide-react";

import CategoryIcon from "~/features/categories/components/category-icon";
import { cn } from "~/lib/utils";

interface Props {
  transaction: Transaction;
  className?: string;
}

export default function TransactionIcon({ transaction, className }: Props) {
  if (transaction.transaction_type === "transfer") {
    return <ArrowRightLeftIcon className={cn("text-gray inline-block size-4", className)} />;
  }

  if (transaction.category) {
    return <CategoryIcon category={transaction.category} className={cn("size-4", className)} />;
  }

  if (transaction.transaction_type === "expense") {
    return <CircleMinusIcon className={cn("text-red inline-block size-4", className)} />;
  }

  if (transaction.transaction_type === "income") {
    return <CirclePlusIcon className={cn("text-green inline-block size-4", className)} />;
  }

  return null;
}
