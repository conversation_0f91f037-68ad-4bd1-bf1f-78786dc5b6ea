import type { Transaction } from "../types";

import { Link } from "@tanstack/react-router";
import { CopyIcon, EditIcon, MoreHorizontalIcon, ReceiptTextIcon, Trash2Icon } from "lucide-react";

import Box from "~/components/blocks/box";
import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { getAccountName } from "~/features/accounts/utils";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { useTransactionActions } from "../hooks";
import TransactionIcon from "./transaction-icon";

interface Props {
  transaction: Transaction;
}

export default function TransactionsListItem({ transaction }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editTransaction, deleteTransaction, copyTransaction } = useTransactionActions(transaction);

  return (
    <Box className="flex items-center p-0">
      <div className="flex items-center gap-6 px-6 py-4">
        <TransactionIcon transaction={transaction} className="size-5 stroke-[1.5]" />
        <p className="text-sm/5">{formatDate(transaction.transaction_date)}</p>
      </div>

      <div className="flex-1 px-6 py-4">
        <Link
          to="/transactions/$transactionId"
          params={{ transactionId: transaction.id }}
          className="text-link hover:text-link/90 block text-sm/5 hover:underline"
        >
          {getAccountName(transaction.account)}
        </Link>
        {transaction.transaction_type === "transfer" && (
          <p className="text-gray text-sm/5">→ {getAccountName(transaction.account_to!)}</p>
        )}
        {transaction.description && <p className="text-gray text-xs/5">{transaction.description}</p>}
      </div>

      <div className="min-w-52 flex-1 px-6 py-4">
        {transaction.category && (
          <p
            className="border-l-2 ps-1.5 text-xs/4 font-semibold uppercase"
            style={{ borderColor: transaction.category.color }}
          >
            <span className="inline-block pt-0.5">{transaction.category.name}</span>
          </p>
        )}
      </div>

      <div className="min-w-36 px-6 py-4">
        <p className="text-sm/5 font-semibold">{formatCurrency(transaction.account.currency, transaction.amount)}</p>
        {transaction.transaction_type !== "transfer" && transaction.account.currency !== baseCurrency && (
          <p className="text-gray text-xs/5">({formatCurrency(baseCurrency, transaction.base_amount)})</p>
        )}
        {transaction.transaction_type === "transfer" && (
          <p className="text-gray text-sm/5">
            → {formatCurrency(transaction.account_to!.currency, transaction.amount_to)}
          </p>
        )}
      </div>

      <div className="px-6 py-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="place-self-start">
              <MoreHorizontalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link to="/transactions/$transactionId" params={{ transactionId: transaction.id }}>
                <ReceiptTextIcon />
                <span className="inline-block py-0.5">Transaction details</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={editTransaction}>
              <EditIcon />
              <span className="inline-block py-0.5">Edit transaction</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={copyTransaction}>
              <CopyIcon />
              <span className="inline-block py-0.5">Copy transaction</span>
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" onClick={deleteTransaction}>
              <Trash2Icon />
              <span className="inline-block py-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Box>
  );
}
