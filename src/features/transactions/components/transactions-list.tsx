import type { Transaction } from "../types";

import TransactionsListItem from "./transactions-list-item";

interface Props {
  transactions: Transaction[];
}

export default function TransactionsList({ transactions }: Props) {
  return (
    <div className="space-y-1">
      {transactions.length === 0 ? (
        <div className="text-muted-foreground py-8 text-center">
          <p>No transactions found</p>
        </div>
      ) : (
        transactions.map((transaction) => (
          <TransactionsListItem key={transaction.id} transaction={transaction} />
        ))
      )}
    </div>
  );
}
