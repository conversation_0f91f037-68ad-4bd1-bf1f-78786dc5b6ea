import { z } from "zod";

import { decimal, nullableString } from "~/lib/schemas";

export const TransactionRequestSchema = z
  .object({
    transaction_date: z.iso.date("Transaction date is required and must be a valid date"),
    transaction_type: z.enum(["income", "expense", "transfer"]),
    description: nullableString(),
    category_id: z
      .union([z.uuid(), z.literal("")])
      .nullable()
      .transform((value) => (value && value.trim().length > 0 ? value : null)),
    account_id: z.uuid("Account is required"),
    amount: decimal().refine((value) => Number(value) > 0, { error: "Amount must be greater than 0" }),
    account_to_id: z
      .union([z.uuid(), z.literal("")])
      .nullable()
      .transform((value) => (value && value.trim().length > 0 ? value : null)),
    amount_to: decimal(),
  })
  .refine((value) => value.transaction_type !== "transfer" || value.account_to_id != null, {
    message: "Transfers must have destination account",
    path: ["account_to_id"],
  })
  .refine((value) => value.transaction_type !== "transfer" || Number(value.amount_to) > 0, {
    message: "Transfers must have destination amount",
    path: ["amount_to"],
  });
