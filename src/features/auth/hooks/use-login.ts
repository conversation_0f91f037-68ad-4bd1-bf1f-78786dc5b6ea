import type { LoginData, TokensResponse, User } from "../types";

import { useMutation } from "@tanstack/react-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { toast } from "sonner";

import { apiClient } from "~/api";

import useAuthStore from "../store";

export function useLogin() {
  const navigate = useNavigate();
  const searchParams = useSearch({ strict: false });
  const setAuthState = useAuthStore((state) => state.setState);

  return useMutation({
    mutationFn: (data: LoginData) => apiClient.post<TokensResponse, LoginData>("/v1/auth/tokens", data),
    onSuccess: async () => {
      const user = await apiClient.get<User>("/v1/users/me");

      setAuthState({ user, isAuthenticated: true });

      toast.success("Welcome back!", { description: "You have been logged in successfully." });

      return navigate({ to: searchParams.next ?? "/" });
    },
  });
}
