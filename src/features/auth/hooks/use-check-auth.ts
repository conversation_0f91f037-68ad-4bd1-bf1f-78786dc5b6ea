import type { User } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";
import router from "~/router";

import useAuthStore from "../store";

export function useCheckAuth() {
  const { isInitialized, isAuthenticated, setState } = useAuthStore(
    useShallow((state) => ({
      isAuthenticated: state.isAuthenticated,
      isInitialized: state.isInitialized,
      setState: state.setState,
    }))
  );

  const checkAuth = useCallback(
    async (signal?: AbortSignal) => {
      try {
        const user = await apiClient.get<User>("/v1/users/me", undefined, undefined, signal);

        setState({ user, isInitialized: true, isAuthenticated: true });

        await router.invalidate();
      } catch (err) {
        if (err instanceof DOMException && err.name === "AbortError") return;

        console.warn((err as Error).message);
        setState({ isInitialized: true, isAuthenticated: false });
      }
    },
    [setState]
  );

  return { isAuthenticated, isInitialized, checkAuth };
}
