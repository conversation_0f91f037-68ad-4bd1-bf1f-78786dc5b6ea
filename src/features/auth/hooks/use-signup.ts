import type { LoginData, SignupData, TokensResponse, User } from "../types";

import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import { apiClient } from "~/api";

import useAuthStore from "../store";

export function useSignup() {
  const navigate = useNavigate();
  const setAuthState = useAuthStore((state) => state.setState);

  return useMutation({
    mutationFn: (data: SignupData) => apiClient.post<User, SignupData>("/v1/users", data),
    onSuccess: async (data, variables) => {
      await apiClient.post<TokensResponse, LoginData>("/v1/auth/tokens", {
        email: data.email,
        password: variables.password,
      });

      setAuthState({ user: data, isAuthenticated: true });

      toast.success("Welcome!", { description: "You have been signed up successfully." });

      return navigate({ to: "/" });
    },
  });
}
