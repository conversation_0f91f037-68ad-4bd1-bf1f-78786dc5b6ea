import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import { apiClient } from "~/api";

import useAuthStore from "../store";

export function useLogout() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const setAuthState = useAuthStore((state) => state.setState);

  return useMutation({
    mutationFn: () => apiClient.delete<void>("/v1/auth/logout"),
    onSuccess: async () => {
      queryClient.removeQueries();
      queryClient.clear();

      setAuthState({ user: null, isAuthenticated: false });

      toast.success("Goodbye!", { description: "You have been logged out successfully." });

      return navigate({ to: "/login" });
    },
  });
}
