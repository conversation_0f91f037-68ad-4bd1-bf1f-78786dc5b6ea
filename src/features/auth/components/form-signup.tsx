import type { SignupData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "@tanstack/react-router";

import ErrorMessage from "~/components/blocks/error-message";
import InputCurrency from "~/components/inputs/input-currency";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useSignup } from "~/features/auth/hooks";

import { SignupSchema } from "../schemas";

export default function FormSignup() {
  const { mutate: signup, error, isPending } = useSignup();

  const form = useForm<SignupData>({
    defaultValues: { email: "", password: "", name: "", base_currency: "USD" },
    resolver: zodResolver(SignupSchema),
  });

  const onSubmit = form.handleSubmit((data) => {
    signup(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="min-w-64 space-y-8">
        {error && <ErrorMessage title="Login failed" error={error} />}

        <div className="flex flex-col gap-2">
          <InputText
            control={form.control}
            name="email"
            type="email"
            placeholder="Email"
            autoComplete="username"
            required
          />

          <InputText control={form.control} name="name" placeholder="Your name (optional)" />

          <InputText
            control={form.control}
            name="password"
            type="password"
            placeholder="Password"
            autoComplete="new-password"
            required
          />

          <InputCurrency control={form.control} name="base_currency" placeholder="Base currency" required />
        </div>

        <div className="flex flex-col gap-2">
          <Button type="submit" className="w-full" disabled={isPending}>
            Create account
          </Button>
          <Button variant="outline" className="w-full" disabled={isPending} asChild>
            <Link to="/login" preload={false}>
              Log in
            </Link>
          </Button>
        </div>
      </form>
    </Form>
  );
}
