import type { LoginData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "@tanstack/react-router";

import ErrorMessage from "~/components/blocks/error-message";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useLogin } from "~/features/auth/hooks";

import { LoginSchema } from "../schemas";

export default function FormLogin() {
  const { mutate: login, error, isPending } = useLogin();

  const form = useForm<LoginData>({
    defaultValues: { email: "", password: "" },
    resolver: zodResolver(LoginSchema),
  });

  const onSubmit = form.handleSubmit((data) => {
    login(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="min-w-64 space-y-8">
        {error && <ErrorMessage title="Login failed" error={error} />}

        <div className="flex flex-col gap-2">
          <InputText
            control={form.control}
            name="email"
            type="email"
            placeholder="Email"
            autoComplete="username"
            required
          />

          <InputText
            control={form.control}
            name="password"
            type="password"
            placeholder="Password"
            autoComplete="current-password"
            required
          />
        </div>

        <div className="flex flex-col gap-2">
          <Button type="submit" className="w-full" disabled={isPending}>
            Log in
          </Button>
          <Button variant="outline" className="w-full" disabled={isPending} asChild>
            <Link to="/signup" preload={false}>
              Sign up
            </Link>
          </Button>
        </div>
      </form>
    </Form>
  );
}
