import type { User } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

interface State {
  isAuthenticated: boolean;
  isInitialized: boolean;
  user: User | null;
}

interface Actions {
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  setInitialized: (isInitialized: boolean) => void;
  setState: (state: Partial<State>) => void;
}

const useAuthStore = create<State & Actions>()(
  immer((set) => ({
    isAuthenticated: false,
    isInitialized: false,
    user: null,

    setUser: (user) => {
      set({ user });
    },
    setAuthenticated: (isAuthenticated) => {
      set({ isAuthenticated });
    },
    setInitialized: (isInitialized) => {
      set({ isInitialized });
    },
    setState: (state) => {
      set(state);
    },
  }))
);

export default useAuthStore;
