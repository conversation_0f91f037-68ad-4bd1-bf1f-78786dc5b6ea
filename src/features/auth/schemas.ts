import { z } from "zod";

import { currencies } from "~/lib/currencies";

export const LoginSchema = z.object({
  email: z.email(),
  password: z.string(),
});

export const SignupSchema = z.object({
  email: z.email(),
  password: z.string().min(6),
  base_currency: z.enum(currencies),
  name: z
    .string()
    .nullable()
    .transform((value) => (value && value.trim().length > 0 ? value : null)),
});
