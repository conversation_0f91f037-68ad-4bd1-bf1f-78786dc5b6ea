import { useEffect } from "react";

import { RouterProvider } from "@tanstack/react-router";
import { LoaderCircleIcon } from "lucide-react";

import { Toaster } from "~/components/ui/sonner";
import { useCheckAuth } from "~/features/auth/hooks";
import ConfirmationDialog from "~/features/ui/confirmations/components/confirmation-dialog";
import router from "~/router";

export default function App() {
  const { isInitialized, isAuthenticated, checkAuth } = useCheckAuth();

  useEffect(() => {
    const abortController = new AbortController();

    if (isInitialized) return;

    void checkAuth(abortController.signal);

    return () => abortController.abort();
  }, [checkAuth, isInitialized]);

  if (!isInitialized) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoaderCircleIcon className="text-primary block size-10 animate-spin" />
      </div>
    );
  }

  return (
    <>
      <RouterProvider router={router} context={{ isAuthenticated }} />

      <ConfirmationDialog />
      <Toaster />
    </>
  );
}
