import { Outlet, redirect } from "@tanstack/react-router";
import { z } from "zod";

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: z.object({ next: z.string().optional() }),
  beforeLoad: ({ context, search }) => {
    if (context.isAuthenticated) {
      // eslint-disable-next-line @typescript-eslint/only-throw-error
      throw redirect({ to: search.next ?? "/" });
    }
  },
});

function RouteComponent() {
  return (
    <div className="mx-auto flex h-full min-h-svh w-full max-w-md flex-col items-center justify-center gap-4 p-4">
      <div className="p-2">
        <img src="/logo.webp" alt="Finanze.Pro" className="aspect-square h-12" />
      </div>

      <main>
        <Outlet />
      </main>
    </div>
  );
}
