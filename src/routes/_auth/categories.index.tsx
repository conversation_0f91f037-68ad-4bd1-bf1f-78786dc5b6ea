import { PlusIcon } from "lucide-react";

import PageHeader from "~/components/blocks/page-header";
import { But<PERSON> } from "~/components/ui/button";
import CategoriesView from "~/features/categories/components/categories-view";
import CategoryDialog from "~/features/categories/components/category-dialog";
import { useCategoryActions } from "~/features/categories/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { createCategory } = useCategoryActions();

  return (
    <>
      <PageHeader title="Categories">
        <div className="flex items-center justify-between gap-2">
          <div></div>

          <div>
            <Button onClick={() => createCategory()}>
              <PlusIcon />
              Add category
            </Button>
          </div>
        </div>
      </PageHeader>

      <CategoriesView />

      <CategoryDialog />
    </>
  );
}
