import { ChevronsDownUpIcon, ChevronsUpDownIcon, MoreVerticalIcon, PlusIcon } from "lucide-react";

import PageHeader from "~/components/blocks/page-header";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import AccountDialog from "~/features/accounts/components/account-dialog";
import AccountsView from "~/features/accounts/components/accounts-view";
import { useAccountActions } from "~/features/accounts/hooks";
import useAccountsStore from "~/features/accounts/store";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { createAccount, importAccounts, exportAccounts, toggleExpandGroups } = useAccountActions();

  const expandGroups = useAccountsStore((state) => state.expandGroups);

  return (
    <>
      <PageHeader title="Accounts">
        <div className="flex items-center justify-between gap-2">
          <div>
            <Button variant="gray" onClick={toggleExpandGroups}>
              {expandGroups ? <ChevronsDownUpIcon className="stroke-3" /> : <ChevronsUpDownIcon className="stroke-3" />}
              {expandGroups ? "Collapse all" : "Expand all"}
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button onClick={() => createAccount()}>
              <PlusIcon /> <span className="inline-block pt-0.5">Add account</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVerticalIcon />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={importAccounts}>Import accounts</DropdownMenuItem>
                <DropdownMenuItem onClick={exportAccounts}>Export accounts</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </PageHeader>

      <AccountsView />

      <AccountDialog />
    </>
  );
}
