import { useMemo, useState } from "react";

import { PlusIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import BudgetDialog from "~/features/budgets/components/budget-dialog";
import BudgetsList from "~/features/budgets/components/budgets-list";
import { useBudgetActions, useBudgets } from "~/features/budgets/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgets, isLoading, error } = useBudgets();
  const { createBudget } = useBudgetActions();

  const [showInactive, setShowInactive] = useState(false);

  const filteredBudgets = useMemo(() => {
    if (showInactive) {
      return budgets.filter((budget) => !budget.is_active);
    }
    return budgets.filter((budget) => budget.is_active);
  }, [budgets, showInactive]);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budgets" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budgets" />
        <ErrorMessage title="Can't load budgets" error={error} />
      </>
    );
  }

  const hasBudgets = budgets.length > 0;

  return (
    <>
      <PageHeader title="Budgets">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Switch id="show-inactive" checked={showInactive} onCheckedChange={setShowInactive} />
            <Label htmlFor="show-inactive" className="text-sm font-medium">
              Show inactive budgets
            </Label>
          </div>
          <div>
            {hasBudgets && (
              <Button onClick={() => createBudget()}>
                <PlusIcon />
                <span className="inline-block pt-0.5">Create Budget</span>
              </Button>
            )}
          </div>
        </div>
      </PageHeader>

      <BudgetsList budgets={filteredBudgets} />
      <BudgetDialog />
    </>
  );
}
