import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import TransactionDetailsOverview from "~/features/transactions/components/transaction-details-overview";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";
import { useTransaction } from "~/features/transactions/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { transactionId } = Route.useParams();

  const { data: transaction, isLoading, error } = useTransaction(transactionId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Transaction details" backLink={{ to: "/transactions" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Transaction details" backLink={{ to: "/transactions" }} />
        <ErrorMessage title="Can't load transaction" error={error} />
      </>
    );
  }

  if (!transaction) {
    return (
      <>
        <PageHeader title="Transaction details" backLink={{ to: "/transactions" }} />
        <ErrorMessage title="Transaction not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Transaction details" backLink={{ to: "/transactions" }} />
      <TransactionDetailsOverview transaction={transaction} />
      <TransactionDialog />
    </>
  );
}
