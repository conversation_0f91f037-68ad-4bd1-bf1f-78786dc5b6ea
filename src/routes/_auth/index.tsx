import { formatDate } from "date-fns";
import { PlusIcon } from "lucide-react";

import PageHeader from "~/components/blocks/page-header";
import { Button } from "~/components/ui/button";
import { useCurrentUser } from "~/features/auth/hooks";
import DashboardOverviewExpenses from "~/features/stats/components/dashboard-overview-expenses";
import DashboardOverviewIncome from "~/features/stats/components/dashboard-overview-income";
import DashboardOverviewTotals from "~/features/stats/components/dashboard-overview-totals";
import { useStatsOverview } from "~/features/stats/hooks";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";
import { useTransactionActions } from "~/features/transactions/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const currentUser = useCurrentUser();

  const { createTransaction } = useTransactionActions();
  const { data: stats, isLoading: isStatsLoading } = useStatsOverview();

  const name = currentUser.name?.trim().length ? currentUser.name : currentUser.email.split("@")[0];

  return (
    <>
      <PageHeader title={`Welcome back, ${name}!`}>
        <div className="flex items-center justify-between gap-2">
          <p className="text-gray text-sm">
            Today is <strong className="font-semibold text-gray-600">{formatDate(new Date(), "PPPP")}</strong>
          </p>

          <div>
            <Button onClick={() => createTransaction()}>
              <PlusIcon />
              <span className="inline-block pt-0.5">Add transaction</span>
            </Button>
          </div>
        </div>
      </PageHeader>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <DashboardOverviewTotals stats={stats} isLoading={isStatsLoading} />
        <DashboardOverviewExpenses stats={stats} isLoading={isStatsLoading} />
        <DashboardOverviewIncome stats={stats} isLoading={isStatsLoading} />
      </div>

      <TransactionDialog />
    </>
  );
}
