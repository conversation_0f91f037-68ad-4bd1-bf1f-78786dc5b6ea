import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import CategoryDetailsOverview from "~/features/categories/components/category-details-overview";
import CategoryDialog from "~/features/categories/components/category-dialog";
import { useCategory } from "~/features/categories/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { categoryId } = Route.useParams();

  const { data: category, isLoading, error } = useCategory(categoryId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <ErrorMessage title="Can't load category" error={error} />
      </>
    );
  }

  if (!category) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <ErrorMessage title="Category not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Category details" backLink={{ to: "/categories" }} />
      <CategoryDetailsOverview category={category} />
      <CategoryDialog />
    </>
  );
}
