import { useMemo, useState } from "react";

import { PlusIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import GoalDialog from "~/features/goals/components/goal-dialog";
import GoalsList from "~/features/goals/components/goals-list";
import { useGoalActions, useGoals } from "~/features/goals/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const [showInactive, setShowInactive] = useState(false);

  const { goals, activeGoals, inactiveGoals, isLoading, error } = useGoals();
  const { createGoal } = useGoalActions();

  const displayedGoals = useMemo(() => {
    if (showInactive) {
      return goals;
    }
    return activeGoals;
  }, [goals, activeGoals, showInactive]);

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (error) {
    return <ErrorMessage title="Failed to load goals" error={error} />;
  }

  return (
    <>
      <PageHeader title="Goals">
        <div className="flex items-center justify-between gap-4">
          <div></div>
          <div>
            <Button onClick={() => createGoal()}>
              <PlusIcon />
              <span className="inline-block pt-0.5">Create Goal</span>
            </Button>
          </div>
        </div>
      </PageHeader>

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch id="show-inactive" checked={showInactive} onCheckedChange={setShowInactive} />
            <Label htmlFor="show-inactive">Show inactive goals ({inactiveGoals.length})</Label>
          </div>
        </div>

        <GoalsList goals={displayedGoals} />
      </div>

      <GoalDialog />
    </>
  );
}
