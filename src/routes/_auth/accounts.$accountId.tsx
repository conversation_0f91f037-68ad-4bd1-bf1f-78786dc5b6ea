import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import AccountDetailsOverview from "~/features/accounts/components/account-details-overview";
import AccountDialog from "~/features/accounts/components/account-dialog";
import { useAccount } from "~/features/accounts/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { accountId } = Route.useParams();

  const { data: account, isLoading, error } = useAccount(accountId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <ErrorMessage title="Can't load account" error={error} />
      </>
    );
  }

  if (!account) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <ErrorMessage title="Account not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
      <AccountDetailsOverview account={account} />
      <AccountDialog />
    </>
  );
}
