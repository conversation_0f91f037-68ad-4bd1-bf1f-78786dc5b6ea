import { PlusIcon } from "lucide-react";

import PageHeader from "~/components/blocks/page-header";
import { But<PERSON> } from "~/components/ui/button";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";
import TransactionsView from "~/features/transactions/components/transactions-view";
import { useTransactionActions } from "~/features/transactions/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { createTransaction } = useTransactionActions();

  return (
    <>
      <PageHeader title="Transactions">
        <div className="flex items-center justify-between gap-2">
          <div></div>

          <div>
            <Button onClick={() => createTransaction()}>
              <PlusIcon />
              <span className="inline-block pt-0.5">Add transaction</span>
            </Button>
          </div>
        </div>
      </PageHeader>

      <TransactionsView />

      <TransactionDialog />
    </>
  );
}
