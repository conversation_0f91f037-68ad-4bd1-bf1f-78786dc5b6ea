import { Outlet, redirect } from "@tanstack/react-router";

import Footer from "~/components/layouts/footer";
import Header from "~/components/layouts/header";

export const Route = createFileRoute({
  component: RouteComponent,
  beforeLoad: ({ context }) => {
    if (!context.isAuthenticated) {
      // eslint-disable-next-line @typescript-eslint/only-throw-error
      throw redirect({ to: "/login", search: { next: location.pathname } });
    }
  },
});

function RouteComponent() {
  return (
    <div className="flex min-h-svh flex-col">
      <Header />

      <main className="app-container flex-grow">
        <Outlet />
      </main>

      <Footer />
    </div>
  );
}
