import * as React from "react";

import { Link } from "@tanstack/react-router";
import { GaugeIcon, HandCoinsIcon, LayoutDashboardIcon, ListIcon, WalletCardsIcon } from "lucide-react";

import { useCurrentUser } from "~/features/auth/hooks";
import { cn } from "~/lib/utils";

import ProfileDropdown from "./profile-dropdown";
import SidebarNavLink from "./sidebar-nav-link";

type Props = React.ComponentProps<"div">;

export default function Sidebar({ className, ...props }: Props) {
  const currentUser = useCurrentUser();

  return (
    <div className={cn("flex h-full flex-col gap-8 bg-white p-4", className)} {...props}>
      <div className="flex items-center gap-4">
        <Link to="/">
          <img src="/logo.webp" className="aspect-square h-11" alt="Finanze.Pro" />
        </Link>
        <h3 className="text-foreground text-xl leading-none font-bold">
          Finanze<span className="text-primary">.Pro</span>
        </h3>
      </div>

      <div className="flex-grow">
        <nav className="-ml-4 flex flex-col gap-3">
          <SidebarNavLink to="/">
            <LayoutDashboardIcon className="mr-2 size-5" /> Overview
          </SidebarNavLink>
          <SidebarNavLink to="/transactions">
            <HandCoinsIcon className="mr-2 size-5" /> Transactions
          </SidebarNavLink>
          <SidebarNavLink to="/budgets">
            <GaugeIcon className="mr-2 size-5" /> Budgets
          </SidebarNavLink>
          <SidebarNavLink to="/categories">
            <ListIcon className="mr-2 size-5" /> Categories
          </SidebarNavLink>
          <SidebarNavLink to="/accounts">
            <WalletCardsIcon className="mr-2 size-5" /> Accounts
          </SidebarNavLink>
        </nav>
      </div>

      <div className="flex items-center gap-4">
        <Link to="/" className="block shrink-0">
          <img src="/user-placeholder.png" className="aspect-square h-11" alt="user avatar" />
        </Link>
        <div className="flex flex-col items-start truncate">
          <span className="text-foreground text-sm leading-5 font-semibold capitalize">
            {currentUser.name || currentUser.email.split("@")[0]}
          </span>
          <span className="truncate text-xs leading-4 text-gray-500">User</span>
        </div>

        <div className="ms-auto shrink-0">
          <ProfileDropdown />
        </div>
      </div>
    </div>
  );
}
