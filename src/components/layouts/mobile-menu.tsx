import { useState } from "react";

import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { MenuIcon } from "lucide-react";

import Sidebar from "~/components/layouts/sidebar";
import { But<PERSON> } from "~/components/ui/button";
import { Sheet, SheetContent, SheetTitle } from "~/components/ui/sheet";

export default function MobileMenu() {
  const [open, setOpen] = useState(false);

  return (
    <div className="block lg:hidden">
      <Button variant="ghost" size="icon" onClick={() => setOpen(true)}>
        <MenuIcon className="size-6" />
      </Button>

      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent aria-describedby={undefined} side="left">
          <VisuallyHidden>
            <SheetTitle>Main Navigation</SheetTitle>
          </VisuallyHidden>

          <Sidebar />
        </SheetContent>
      </Sheet>
    </div>
  );
}
