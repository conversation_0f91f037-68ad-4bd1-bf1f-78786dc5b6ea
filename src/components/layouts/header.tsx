import { Link } from "@tanstack/react-router";
import { ChevronDownIcon } from "lucide-react";

import MobileMenu from "~/components/layouts/mobile-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useCurrentUser } from "~/features/auth/hooks";

import NavLink from "./nav-link";
import ProfileDropdown from "./profile-dropdown";

export default function Header() {
  const currentUser = useCurrentUser();

  const name = currentUser.name?.length ? currentUser.name : currentUser.email.split("@")[0];

  return (
    <div className="bg-white">
      <div className="app-container flex justify-between gap-6 py-4 lg:py-0">
        <div className="flex items-center gap-4">
          <Link to="/">
            <img src="/logo.webp" className="aspect-square h-11" alt="Finanze.Pro" />
          </Link>
          <h3 className="text-foreground text-xl leading-none font-bold">
            Finanze<span className="text-primary">.Pro</span>
          </h3>
        </div>

        <div className="hidden lg:flex">
          <NavLink to="/">Home</NavLink>
          <NavLink to="/transactions">Transactions</NavLink>
          <NavLink to="/budgets">Budgets</NavLink>

          <DropdownMenu>
            <DropdownMenuTrigger className="hover:border-b-accent flex cursor-pointer items-center gap-2 border-b-8 border-b-white px-8 pt-6 pb-4 text-center text-sm leading-5 font-semibold text-gray-600 uppercase">
              <span>More</span> <ChevronDownIcon className="size-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to="/accounts">Accounts</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to="/categories">Categories</Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-4">
          <div className="hidden lg:flex lg:flex-col lg:items-end">
            <span className="text-foreground text-sm leading-5 font-semibold">{name}</span>
            <span className="text-xs leading-4 text-gray-500">User</span>
          </div>

          <Link to="/" className="hidden lg:block">
            <img src="/user-placeholder.png" className="aspect-square h-11" alt="user avatar" />
          </Link>

          <ProfileDropdown />
          <MobileMenu />
        </div>
      </div>
    </div>
  );
}
