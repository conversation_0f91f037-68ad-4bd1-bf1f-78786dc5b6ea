import type { Control, FieldPath, FieldValues } from "react-hook-form";

import * as React from "react";

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { cn } from "~/lib/utils";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type Option = {
  value: string;
  label: string;
};

type ComponentProps = {
  options: Array<Option>;
  label: string;
  description?: React.ReactNode;
  orientation?: "horizontal" | "vertical";
  className?: string;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> &
  ComponentProps &
  Omit<React.ComponentPropsWithoutRef<typeof RadioGroup>, keyof ComponentProps | "name">;

export default function InputRadioGroup<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { options, label, control, name, className, description, orientation = "horizontal", ...rest } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              value={field.value}
              className={cn(
                orientation === "horizontal" ? "flex flex-row space-x-4" : "flex flex-col space-y-2",
                "mt-2"
              )}
              {...rest}
            >
              {options.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={option.value} id={`${name}-${option.value}`} />
                  <label
                    htmlFor={`${name}-${option.value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </RadioGroup>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
