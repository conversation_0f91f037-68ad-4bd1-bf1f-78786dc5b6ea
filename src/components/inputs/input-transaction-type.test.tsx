import { useForm } from "react-hook-form";

import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";

import { Form } from "~/components/ui/form";

import InputTransactionType from "./input-transaction-type";

// Test wrapper component
function TestWrapper() {
  const form = useForm({
    defaultValues: {
      transaction_type: "expense",
    },
  });

  const options = [
    { value: "income", label: "Income" },
    { value: "expense", label: "Expense" },
    { value: "transfer", label: "Transfer" },
  ];

  return (
    <Form {...form}>
      <InputTransactionType control={form.control} name="transaction_type" options={options} label="Transaction Type" />
    </Form>
  );
}

describe("InputTransactionType", () => {
  it("renders all transaction type options", () => {
    render(<TestWrapper />);

    expect(screen.getByText("Transaction Type")).toBeInTheDocument();
    expect(screen.getByText("Income")).toBeInTheDocument();
    expect(screen.getByText("Expense")).toBeInTheDocument();
    expect(screen.getByText("Transfer")).toBeInTheDocument();
  });

  it("shows the default selected option with primary styling", () => {
    render(<TestWrapper />);

    const expenseButton = screen.getByText("Expense");
    expect(expenseButton).toHaveClass("bg-primary", "text-primary-foreground");
  });

  it("changes selection when clicking different options", () => {
    render(<TestWrapper />);

    const incomeButton = screen.getByText("Income");
    const expenseButton = screen.getByText("Expense");

    // Initially expense should be selected
    expect(expenseButton).toHaveClass("bg-primary", "text-primary-foreground");
    expect(incomeButton).toHaveClass("bg-transparent", "text-foreground");

    // Click income button
    fireEvent.click(incomeButton);

    // Now income should be selected
    expect(incomeButton).toHaveClass("bg-primary", "text-primary-foreground");
    expect(expenseButton).toHaveClass("bg-transparent", "text-foreground");
  });

  it("applies disabled state correctly", () => {
    function DisabledTestWrapper() {
      const form = useForm({
        defaultValues: {
          transaction_type: "expense",
        },
      });

      const options = [
        { value: "income", label: "Income" },
        { value: "expense", label: "Expense" },
        { value: "transfer", label: "Transfer" },
      ];

      return (
        <Form {...form}>
          <InputTransactionType
            control={form.control}
            name="transaction_type"
            options={options}
            label="Transaction Type"
            disabled
          />
        </Form>
      );
    }

    render(<DisabledTestWrapper />);

    const buttons = screen.getAllByRole("button");
    buttons.forEach((button) => {
      expect(button).toBeDisabled();
      expect(button).toHaveClass("disabled:pointer-events-none", "disabled:opacity-50");
    });
  });

  it("has proper container styling", () => {
    render(<TestWrapper />);

    const container = screen.getByText("Income").parentElement;
    expect(container).toHaveClass("bg-white", "p-1", "rounded-md", "flex", "gap-1");
  });

  it("buttons have equal flex styling", () => {
    render(<TestWrapper />);

    const buttons = screen.getAllByRole("button");
    buttons.forEach((button) => {
      expect(button).toHaveClass("flex-1");
    });
  });
});
