import type { Control, FieldPath, FieldValues } from "react-hook-form";

import * as React from "react";

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { cn } from "~/lib/utils";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type Option = {
  value: string;
  label: string;
};

type ComponentProps = {
  options: Array<Option>;
  label: string;
  description?: React.ReactNode;
  className?: string;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> &
  ComponentProps &
  Omit<React.ComponentPropsWithoutRef<"div">, keyof ComponentProps | "name"> & {
    disabled?: boolean;
  };

export default function InputTransactionType<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { options, label, control, name, className, description, disabled, ...rest } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="flex gap-1 rounded-md bg-white p-1" {...rest}>
              {options.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => field.onChange(option.value)}
                  disabled={disabled}
                  className={cn(
                    "flex-1 rounded-sm px-4 py-2 text-sm font-medium transition-colors",
                    "disabled:pointer-events-none disabled:opacity-50",
                    field.value === option.value
                      ? "bg-primary text-primary-foreground hover:bg-primary/90"
                      : "text-foreground bg-transparent hover:bg-gray-100"
                  )}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
