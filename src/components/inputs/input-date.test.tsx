import { useForm } from "react-hook-form";

import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";

import { Form } from "~/components/ui/form";

import InputDate from "./input-date";

// Test wrapper component
function TestWrapper() {
  const form = useForm({
    defaultValues: {
      test_date: "2024-01-15",
    },
  });

  return (
    <Form {...form}>
      <InputDate control={form.control} name="test_date" label="Test Date" placeholder="Select a date" />
    </Form>
  );
}

describe("InputDate", () => {
  it("renders with label and trigger button", () => {
    render(<TestWrapper />);

    expect(screen.getByText("Test Date")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
    // Check that some date text is displayed (format may vary by locale)
    expect(screen.getByRole("button")).toHaveTextContent(/2024/);
  });

  it("shows placeholder when no value is set", () => {
    function EmptyTestWrapper() {
      const form = useForm({
        defaultValues: {
          test_date: "",
        },
      });

      return (
        <Form {...form}>
          <InputDate control={form.control} name="test_date" label="Test Date" placeholder="Select a date" />
        </Form>
      );
    }

    render(<EmptyTestWrapper />);
    expect(screen.getByText("Select a date")).toBeInTheDocument();
  });

  it("opens calendar popover when clicked", () => {
    render(<TestWrapper />);

    const triggerButton = screen.getByRole("button");
    fireEvent.click(triggerButton);

    // Calendar should be visible
    expect(screen.getByRole("grid")).toBeInTheDocument(); // Calendar grid
  });

  it("applies disabled state correctly", () => {
    function DisabledTestWrapper() {
      const form = useForm({
        defaultValues: {
          test_date: "2024-01-15",
        },
      });

      return (
        <Form {...form}>
          <InputDate control={form.control} name="test_date" label="Test Date" disabled />
        </Form>
      );
    }

    render(<DisabledTestWrapper />);

    const triggerButton = screen.getByRole("button");
    expect(triggerButton).toBeDisabled();
  });

  it("displays calendar icon", () => {
    render(<TestWrapper />);

    const calendarIcon = screen.getByRole("button").querySelector("svg");
    expect(calendarIcon).toBeInTheDocument();
  });
});
