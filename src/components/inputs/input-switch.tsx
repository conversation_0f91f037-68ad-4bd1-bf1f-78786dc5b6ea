import type { Control, FieldPath, FieldValues } from "react-hook-form";

import * as React from "react";

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Switch } from "~/components/ui/switch";
import { cn } from "~/lib/utils";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type ComponentProps = {
  label: string;
  description?: React.ReactNode;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> &
  ComponentProps &
  Omit<React.ComponentPropsWithoutRef<typeof Switch>, keyof ComponentProps | "name">;

export default function InputSwitch<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { label, control, name, className, description, ...rest } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { value, onChange, ...field } }) => (
        <>
          <FormItem
            className={cn(
              "border-input flex flex-row items-center justify-between rounded-lg border bg-white p-4",
              className
            )}
          >
            <div className="space-y-0.5">
              <FormLabel className="text-sm">{label}</FormLabel>
              {description && <FormDescription>{description}</FormDescription>}
              <FormMessage />
            </div>
            <FormControl>
              <Switch checked={value} onCheckedChange={onChange} {...field} {...rest} />
            </FormControl>
          </FormItem>
        </>
      )}
    />
  );
}
