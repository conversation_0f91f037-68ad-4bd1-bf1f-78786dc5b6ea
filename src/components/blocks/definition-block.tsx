import * as React from "react";

import { cn } from "~/lib/utils";

type Props = React.PropsWithChildren<{ title: string; size?: "sm" | "md" | "default"; className?: string }>;

export default function DefinitionBlock({ title, children, size = "default", className }: Props) {
  return (
    <dl className={cn("flex flex-col", className)}>
      <dt className="text-gray text-xs/5 font-semibold uppercase">{title}</dt>
      <dd
        className={cn("text-foreground", {
          "text-sm/5 font-medium": size === "sm",
          "text-xl/6 font-semibold": size == "md",
          "text-2xl/8 font-bold": size === "default",
        })}
      >
        {children}
      </dd>
    </dl>
  );
}
