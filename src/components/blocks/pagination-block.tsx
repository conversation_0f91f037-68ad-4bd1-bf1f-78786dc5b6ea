import type { PaginationMeta } from "~/types";

import { useMemo } from "react";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination";

interface Props {
  pagination: PaginationMeta;
  currentPage: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export default function PaginationBlock({ pagination, currentPage, onPageChange, className = "" }: Props) {
  // Generate page numbers to display
  const pages = useMemo(() => {
    const { page: currentPage, pages: totalPages } = pagination;
    const pageNumbers = [];

    // Always show the first page
    pageNumbers.push(1);

    // Show ellipsis if needed
    if (currentPage > 3) {
      pageNumbers.push("ellipsis-start");
    }

    // Show the current page and surrounding pages
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i !== 1 && i !== totalPages) {
        pageNumbers.push(i);
      }
    }

    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      pageNumbers.push("ellipsis-end");
    }

    // Always show the last page if there's more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  }, [pagination]);

  if (pagination.pages <= 1) {
    return null;
  }

  return (
    <Pagination className={className}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={() => onPageChange(currentPage - 1)}
            className={!pagination.has_prev ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>

        {pages.map((pageNumber, index) => {
          if (pageNumber === "ellipsis-start" || pageNumber === "ellipsis-end") {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }

          return (
            <PaginationItem key={pageNumber}>
              <PaginationLink
                isActive={pageNumber === pagination.page}
                onClick={() => onPageChange(pageNumber as number)}
              >
                {pageNumber}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        <PaginationItem>
          <PaginationNext
            onClick={() => onPageChange(currentPage + 1)}
            className={!pagination.has_next ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
