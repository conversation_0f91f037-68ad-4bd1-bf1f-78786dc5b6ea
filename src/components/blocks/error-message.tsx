import type { ApiError } from "~/api/client";

import { useMemo } from "react";

import { AlertCircleIcon } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";

interface Props {
  title: string;
  error?: Error | ApiError;
}

export default function ErrorMessage({ title, error }: Props) {
  const description = useMemo(() => {
    if (!error) return undefined;

    return (
      <>
        <p>{error.message}</p>
        {/* todo: show info from 'details' field */}
      </>
    );
  }, [error]);

  return (
    <Alert variant="warning">
      <AlertCircleIcon />
      <AlertTitle>{title}</AlertTitle>
      {description && <AlertDescription>{description}</AlertDescription>}
    </Alert>
  );
}
