export interface ApiError {
  message: string;
  details?: Record<string, unknown>;
}

type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

interface RequestOptions<TBody = unknown> {
  method: HttpMethod;
  headers?: HeadersInit;
  body?: TBody;
  queryParams?: Record<string, string>;
  signal?: AbortSignal;
}

const buildUrl = (url: string, queryParams?: Record<string, string>): string => {
  if (!queryParams) return url;
  const searchParams = new URLSearchParams();
  Object.entries(queryParams).forEach(([key, value]) => {
    searchParams.append(key, value);
  });
  return `${url}?${searchParams.toString()}`;
};

const getCsrfToken = (): string | undefined => {
  const cookies = document.cookie.split(";");
  const csrfCookie = cookies.find((cookie) => cookie.trim().startsWith("csrf_access_token="));
  return csrfCookie ? csrfCookie.trim().substring("csrf_access_token=".length) : undefined;
};

export class ApiClient {
  constructor(private baseUrl: string) {}

  private async request<TResponse, TBody = undefined, TError = ApiError>(
    endpoint: string,
    options: RequestOptions<TBody>
  ): Promise<TResponse> {
    const url = buildUrl(`${this.baseUrl}${endpoint}`, options.queryParams);

    let headers: HeadersInit = {
      ...options.headers,
    };

    if (options.body && !(options.body instanceof FormData)) {
      headers = {
        ...headers,
        "Content-Type": "application/json",
      };
    }

    // Add CSRF token to non-GET requests
    if (options.method !== "GET") {
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        headers = {
          ...headers,
          "X-CSRF-TOKEN": csrfToken,
        };
      }
    }

    const response = await fetch(url, {
      method: options.method,
      headers,
      signal: options.signal,
      ...(options.body && { body: options.body instanceof FormData ? options.body : JSON.stringify(options.body) }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw error as TError;
    }

    // Check if the response has content before parsing JSON
    const contentLength = response.headers.get("content-length");
    if (contentLength && parseInt(contentLength) > 0) {
      return response.json();
    }

    return {} as TResponse;
  }

  async get<TResponse, TError = ApiError>(
    endpoint: string,
    queryParams?: Record<string, string>,
    headers?: HeadersInit,
    signal?: AbortSignal
  ) {
    return this.request<TResponse, undefined, TError>(endpoint, {
      method: "GET",
      headers,
      queryParams,
      signal,
    });
  }

  async post<TResponse, TBody, TError = ApiError>(
    endpoint: string,
    body: TBody,
    headers?: HeadersInit,
    signal?: AbortSignal
  ) {
    return this.request<TResponse, TBody, TError>(endpoint, {
      method: "POST",
      body,
      headers,
      signal,
    });
  }

  async put<TResponse, TBody, TError = ApiError>(
    endpoint: string,
    body: TBody,
    headers?: HeadersInit,
    signal?: AbortSignal
  ) {
    return this.request<TResponse, TBody, TError>(endpoint, {
      method: "PUT",
      body,
      headers,
      signal,
    });
  }

  async patch<TResponse, TBody, TError = ApiError>(
    endpoint: string,
    body: TBody,
    headers?: HeadersInit,
    signal?: AbortSignal
  ) {
    return this.request<TResponse, TBody, TError>(endpoint, {
      method: "PATCH",
      body,
      headers,
      signal,
    });
  }

  async delete<TResponse, TError = ApiError>(endpoint: string, headers?: HeadersInit, signal?: AbortSignal) {
    return this.request<TResponse, undefined, TError>(endpoint, {
      method: "DELETE",
      headers,
      signal,
    });
  }
}
