FROM node:22-alpine AS builder

WORKDIR /app

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

RUN corepack enable
RUN pnpm install --frozen-lockfile

COPY src/ ./src/
COPY public/ ./public/
COPY index.html vite.config.ts tsconfig.json tsconfig.*.json ./

RUN pnpm run build

FROM nginx:stable-alpine

LABEL org.opencontainers.image.source="https://github.com/finanze-pro/webapp"

WORKDIR /app

COPY --from=builder /app/dist/ /usr/share/nginx/html/
COPY ./nginx.conf  /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
